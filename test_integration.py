#!/usr/bin/env python3
"""
Integration Test for Enhanced Retrieval System

This script tests that the enhanced features are properly integrated into the main query service.
"""

import sys
import os
import time
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_configuration_loading():
    """Test that enhanced configuration loads properly"""
    logger.info("Testing enhanced configuration loading...")
    
    try:
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        
        # Test basic configuration
        assert hasattr(config, 'enable_enhanced_retrieval')
        assert hasattr(config, 'enable_performance_monitoring')
        assert hasattr(config, 'query_expansion')
        assert hasattr(config, 'result_diversification')
        
        logger.info(f"✓ Enhanced configuration loaded successfully")
        logger.info(f"  Enhanced retrieval enabled: {config.enable_enhanced_retrieval}")
        logger.info(f"  Performance monitoring enabled: {config.enable_performance_monitoring}")
        logger.info(f"  Query expansion enabled: {config.query_expansion.enable_entity_extraction}")
        logger.info(f"  Result diversification enabled: {config.result_diversification.enable_maximal_marginal_relevance}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration loading failed: {e}")
        return False

def test_query_service_imports():
    """Test that query service imports all enhanced features"""
    logger.info("Testing query service imports...")
    
    try:
        # Test that all enhanced imports are available
        from app.services.query_service import (
            analyze_query_semantics, QueryIntent,
            score_documents_enhanced_batch, ScoredDocument,
            retrieve_documents_multi_stage, MultiStageResult,
            hybrid_search, SearchStrategy,
            retrieve_cross_category, CrossCategoryResult,
            understand_query, expand_query, generate_query_variations,
            diversify_results,
            get_enhanced_retrieval_config, EnhancedRetrievalConfig
        )
        
        logger.info("✓ All enhanced imports successful")
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        return False

def test_semantic_analysis_integration():
    """Test that semantic analysis is properly integrated"""
    logger.info("Testing semantic analysis integration...")
    
    try:
        from app.services.semantic_analyzer import analyze_query_semantics, QueryIntent
        
        # Test semantic analysis
        query = "What are the environmental impacts of deforestation in Southeast Asia?"
        analysis = analyze_query_semantics(query)
        
        assert hasattr(analysis, 'complexity')
        assert hasattr(analysis.complexity, 'intent')
        assert hasattr(analysis.complexity, 'complexity_level')
        assert hasattr(analysis, 'domain_keywords')
        
        logger.info(f"✓ Semantic analysis working")
        logger.info(f"  Intent: {analysis.complexity.intent.value}")
        logger.info(f"  Complexity: {analysis.complexity.complexity_level}")
        logger.info(f"  Domain keywords: {analysis.domain_keywords}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Semantic analysis failed: {e}")
        return False

def test_query_expansion_integration():
    """Test that query expansion is properly integrated"""
    logger.info("Testing query expansion integration...")
    
    try:
        from app.services.query_expander import understand_query, expand_query
        
        # Test query understanding
        query = "sustainable forest management practices"
        understanding = understand_query(query)
        
        assert hasattr(understanding, 'entities')
        assert hasattr(understanding, 'domain_specific_terms')
        assert hasattr(understanding, 'confidence')
        
        logger.info(f"✓ Query understanding working")
        logger.info(f"  Entities: {understanding.entities}")
        logger.info(f"  Domain terms: {understanding.domain_specific_terms}")
        logger.info(f"  Confidence: {understanding.confidence:.3f}")
        
        # Test query expansion
        expansion = expand_query(query, strategy="comprehensive")
        
        assert hasattr(expansion, 'expanded_queries')
        assert hasattr(expansion, 'expansion_type')
        assert hasattr(expansion, 'confidence')
        
        logger.info(f"✓ Query expansion working")
        logger.info(f"  Expansion type: {expansion.expansion_type}")
        logger.info(f"  Expanded queries: {len(expansion.expanded_queries)}")
        logger.info(f"  Confidence: {expansion.confidence:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Query expansion failed: {e}")
        return False

def test_result_diversification_integration():
    """Test that result diversification is properly integrated"""
    logger.info("Testing result diversification integration...")
    
    try:
        from app.services.result_diversifier import diversify_results
        from langchain.schema import Document
        
        # Create sample documents
        sample_docs = [
            Document(
                page_content="Sustainable forest management practices include selective logging and reforestation.",
                metadata={"source": "doc1", "category": "forestry"}
            ),
            Document(
                page_content="Environmental impacts of deforestation include habitat loss and climate change.",
                metadata={"source": "doc2", "category": "environmental"}
            ),
            Document(
                page_content="Forest management regulations require proper permits and environmental assessments.",
                metadata={"source": "doc3", "category": "regulatory"}
            )
        ]
        
        # Test result diversification
        query = "sustainable forest management"
        result = diversify_results(sample_docs, query, strategy="balanced", max_results=2)
        
        assert hasattr(result, 'documents')
        assert hasattr(result, 'diversity_score')
        assert hasattr(result, 'coverage_score')
        assert hasattr(result, 'ranking_strategy')
        
        logger.info(f"✓ Result diversification working")
        logger.info(f"  Documents: {len(result.documents)}")
        logger.info(f"  Diversity score: {result.diversity_score:.3f}")
        logger.info(f"  Coverage score: {result.coverage_score:.3f}")
        logger.info(f"  Strategy: {result.ranking_strategy}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Result diversification failed: {e}")
        return False

def test_performance_monitoring_integration():
    """Test that performance monitoring is properly integrated"""
    logger.info("Testing performance monitoring integration...")
    
    try:
        from app.utils.rag_performance import (
            get_rag_monitor, track_query_expansion_metrics, 
            track_result_diversification_metrics
        )
        
        # Test monitor access
        monitor = get_rag_monitor()
        assert monitor is not None
        
        # Test tracking functions
        track_query_expansion_metrics(
            operation_type="test",
            execution_time=0.1,
            documents_processed=1,
            expansions_generated=3,
            confidence_score=0.8
        )
        
        track_result_diversification_metrics(
            operation_type="test",
            execution_time=0.1,
            documents_processed=3,
            diversity_score=0.7,
            coverage_score=0.8,
            ranking_strategy="balanced"
        )
        
        logger.info("✓ Performance monitoring working")
        return True
        
    except Exception as e:
        logger.error(f"✗ Performance monitoring failed: {e}")
        return False

def test_environment_variables():
    """Test that environment variables control feature flags"""
    logger.info("Testing environment variable control...")
    
    try:
        import os
        
        # Test default values
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        
        logger.info(f"✓ Environment variable defaults working")
        logger.info(f"  ENABLE_ENHANCED_RETRIEVAL: {config.enable_enhanced_retrieval}")
        logger.info(f"  ENABLE_QUERY_EXPANSION: {config.query_expansion.enable_entity_extraction}")
        logger.info(f"  ENABLE_RESULT_DIVERSIFICATION: {config.result_diversification.enable_maximal_marginal_relevance}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Environment variable test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    logger.info("=" * 60)
    logger.info("Enhanced Retrieval System Integration Test")
    logger.info("=" * 60)
    
    start_time = time.time()
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Configuration loading
    if test_enhanced_configuration_loading():
        tests_passed += 1
        logger.info("✓ Test 1/6: Configuration loading - PASSED")
    else:
        logger.error("✗ Test 1/6: Configuration loading - FAILED")
    
    # Test 2: Query service imports
    if test_query_service_imports():
        tests_passed += 1
        logger.info("✓ Test 2/6: Query service imports - PASSED")
    else:
        logger.error("✗ Test 2/6: Query service imports - FAILED")
    
    # Test 3: Semantic analysis integration
    if test_semantic_analysis_integration():
        tests_passed += 1
        logger.info("✓ Test 3/6: Semantic analysis integration - PASSED")
    else:
        logger.error("✗ Test 3/6: Semantic analysis integration - FAILED")
    
    # Test 4: Query expansion integration
    if test_query_expansion_integration():
        tests_passed += 1
        logger.info("✓ Test 4/6: Query expansion integration - PASSED")
    else:
        logger.error("✗ Test 4/6: Query expansion integration - FAILED")
    
    # Test 5: Result diversification integration
    if test_result_diversification_integration():
        tests_passed += 1
        logger.info("✓ Test 5/6: Result diversification integration - PASSED")
    else:
        logger.error("✗ Test 5/6: Result diversification integration - FAILED")
    
    # Test 6: Performance monitoring integration
    if test_performance_monitoring_integration():
        tests_passed += 1
        logger.info("✓ Test 6/6: Performance monitoring integration - PASSED")
    else:
        logger.error("✗ Test 6/6: Performance monitoring integration - FAILED")
    
    # Test 7: Environment variables (bonus test)
    if test_environment_variables():
        logger.info("✓ Bonus Test: Environment variables - PASSED")
    else:
        logger.error("✗ Bonus Test: Environment variables - FAILED")
    
    # Summary
    execution_time = time.time() - start_time
    logger.info("=" * 60)
    logger.info("Integration Test Results Summary")
    logger.info("=" * 60)
    logger.info(f"Tests passed: {tests_passed}/{total_tests}")
    logger.info(f"Success rate: {(tests_passed/total_tests)*100:.1f}%")
    logger.info(f"Execution time: {execution_time:.2f} seconds")
    
    if tests_passed == total_tests:
        logger.info("🎉 All integration tests passed! Enhanced features are properly integrated.")
        return True
    else:
        logger.error(f"❌ {total_tests - tests_passed} tests failed. Please review the integration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 