#!/usr/bin/env python3
"""
Phase 5: Advanced Analytics and Monitoring Test Suite

This script tests the new analytics and performance tuning features:
- Retrieval quality analytics
- Performance bottleneck analysis
- Intelligent performance tuning
- System monitoring and reporting
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_imports():
    """Test that all Phase 5 modules can be imported"""
    try:
        # Core analytics modules
        from app.services.retrieval_analytics import (
            get_retrieval_analytics, record_query_analytics, 
            QueryOutcome, RelevanceLevel, RetrievalAnalytics
        )
        
        # Performance tuning modules
        from app.services.performance_tuner import (
            get_performance_tuner, auto_tune_performance,
            TuningStrategy, TuningAction, PerformanceTuner
        )
        
        # Configuration
        from config.enhanced_retrieval_config import (
            get_enhanced_retrieval_config, AnalyticsConfig, PerformanceTuningConfig
        )
        
        # Performance monitoring
        from app.utils.rag_performance import (
            track_analytics_metrics, track_performance_tuning_metrics,
            track_retrieval_quality_metrics, track_bottleneck_analysis_metrics
        )
        
        logger.info("✅ All Phase 5 modules imported successfully")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False


def test_retrieval_analytics():
    """Test retrieval analytics functionality"""
    try:
        from app.services.retrieval_analytics import (
            get_retrieval_analytics, record_query_analytics, QueryOutcome
        )
        
        analytics = get_retrieval_analytics()
        
        # Test recording query analytics
        record_query_analytics(
            query_id="test_query_1",
            query_text="forestry management practices",
            category="CANOPY",
            outcome=QueryOutcome.SUCCESS,
            execution_time=1.2,
            documents_retrieved=50,
            documents_returned=5,
            cache_hit=True,
            relevance_scores=[0.9, 0.8, 0.7, 0.6, 0.5],
            user_feedback=4
        )
        
        # Test recording failed query
        record_query_analytics(
            query_id="test_query_2",
            query_text="invalid query",
            category="RISE",
            outcome=QueryOutcome.FAILURE,
            execution_time=0.5,
            documents_retrieved=0,
            documents_returned=0,
            cache_hit=False,
            relevance_scores=[],
            error_message="No documents found"
        )
        
        # Test system analytics
        system_analytics = analytics.get_system_analytics(time_window_hours=1)
        assert 'total_queries' in system_analytics
        assert 'success_rate' in system_analytics
        assert 'average_execution_time' in system_analytics
        
        # Test bottleneck analysis
        bottleneck_analysis = analytics.get_bottleneck_analysis()
        assert 'analysis_available' in bottleneck_analysis
        
        # Test retrieval quality metrics
        quality_metrics = analytics.get_retrieval_quality_metrics("test_query_1")
        if quality_metrics:
            assert hasattr(quality_metrics, 'precision_at_k')
            assert hasattr(quality_metrics, 'recall_at_k')
            assert hasattr(quality_metrics, 'f1_score_at_k')
        
        logger.info("✅ Retrieval analytics tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Retrieval analytics test failed: {e}")
        return False


def test_performance_tuner():
    """Test performance tuning functionality"""
    try:
        from app.services.performance_tuner import (
            get_performance_tuner, TuningStrategy, TuningAction
        )
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        tuner = get_performance_tuner(config)
        
        # Test setting tuning strategy
        tuner.set_tuning_strategy(TuningStrategy.ADAPTIVE)
        
        # Test getting tuning status
        status = tuner.get_tuning_status()
        assert 'last_tuning_time' in status
        assert 'tuning_active' in status
        assert 'current_strategy' in status
        assert 'pending_recommendations' in status
        
        # Test updating thresholds
        new_thresholds = {
            'execution_time_warning': 1.5,
            'cache_hit_rate_minimum': 0.4
        }
        tuner.update_thresholds(new_thresholds)
        
        # Test auto-tuning (should not fail even with no data)
        results = tuner.analyze_and_tune()
        assert isinstance(results, list)
        
        logger.info("✅ Performance tuner tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance tuner test failed: {e}")
        return False


def test_analytics_configuration():
    """Test analytics configuration"""
    try:
        from config.enhanced_retrieval_config import (
            get_enhanced_retrieval_config, AnalyticsConfig, PerformanceTuningConfig
        )
        
        config = get_enhanced_retrieval_config()
        
        # Test analytics config
        assert hasattr(config, 'analytics')
        assert isinstance(config.analytics, AnalyticsConfig)
        assert config.analytics.max_history_size == 10000
        assert config.analytics.retention_days == 30
        assert config.analytics.enable_quality_metrics is True
        
        # Test performance tuning config
        assert hasattr(config, 'performance_tuning')
        assert isinstance(config.performance_tuning, PerformanceTuningConfig)
        assert config.performance_tuning.default_strategy == 'adaptive'
        assert config.performance_tuning.enable_auto_tuning is True
        assert config.performance_tuning.tuning_interval_minutes == 30
        
        # Test configuration updates
        config.analytics.max_history_size = 5000
        config.performance_tuning.tuning_interval_minutes = 15
        
        assert config.analytics.max_history_size == 5000
        assert config.performance_tuning.tuning_interval_minutes == 15
        
        logger.info("✅ Analytics configuration tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Analytics configuration test failed: {e}")
        return False


def test_performance_monitoring_integration():
    """Test performance monitoring integration"""
    try:
        from app.utils.rag_performance import (
            track_analytics_metrics, track_performance_tuning_metrics,
            track_retrieval_quality_metrics, track_bottleneck_analysis_metrics
        )
        
        # Test analytics metrics tracking
        track_analytics_metrics(
            operation_type="system_analysis",
            execution_time=0.5,
            documents_processed=100,
            cache_hits=30,
            cache_misses=70,
            relevance_scores=[0.8, 0.7, 0.6],
            user_feedback=4
        )
        
        # Test performance tuning metrics tracking
        track_performance_tuning_metrics(
            tuning_action="increase_cache_ttl",
            execution_time=0.1,
            success=True,
            impact_metrics={'ttl_increase_percent': 50.0, 'new_ttl_seconds': 5400}
        )
        
        # Test retrieval quality metrics tracking
        track_retrieval_quality_metrics(
            query_id="test_quality_query",
            precision_at_k=0.8,
            recall_at_k=0.7,
            f1_score_at_k=0.75,
            mrr=0.6,
            ndcg=0.8,
            avg_relevance_score=0.75,
            diversity_score=0.3,
            coverage_score=0.8
        )
        
        # Test bottleneck analysis metrics tracking
        track_bottleneck_analysis_metrics(
            analysis_type="system_wide",
            execution_time=1.0,
            bottlenecks_found=2,
            recommendations_generated=3,
            critical_issues=1
        )
        
        logger.info("✅ Performance monitoring integration tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance monitoring integration test failed: {e}")
        return False


def test_analytics_export():
    """Test analytics export functionality"""
    try:
        from app.services.retrieval_analytics import get_retrieval_analytics
        
        analytics = get_retrieval_analytics()
        
        # Add some test data
        from app.services.retrieval_analytics import record_query_analytics, QueryOutcome
        
        for i in range(5):
            record_query_analytics(
                query_id=f"export_test_{i}",
                query_text=f"test query {i}",
                category="CANOPY" if i % 2 == 0 else "RISE",
                outcome=QueryOutcome.SUCCESS,
                execution_time=1.0 + i * 0.1,
                documents_retrieved=50,
                documents_returned=5,
                cache_hit=i % 2 == 0,
                relevance_scores=[0.9, 0.8, 0.7, 0.6, 0.5]
            )
        
        # Test export
        export_data = analytics.export_analytics(format='json')
        assert isinstance(export_data, str)
        assert 'system_analytics' in export_data
        assert 'bottleneck_analysis' in export_data
        assert 'category_analytics' in export_data
        
        # Test invalid format
        try:
            analytics.export_analytics(format='invalid')
            assert False, "Should have raised ValueError"
        except ValueError:
            pass  # Expected
        
        logger.info("✅ Analytics export tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Analytics export test failed: {e}")
        return False


def test_tuning_recommendations():
    """Test tuning recommendation generation"""
    try:
        from app.services.performance_tuner import (
            get_performance_tuner, TuningStrategy
        )
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        tuner = get_performance_tuner(config)
        
        # Test with different strategies
        strategies = [TuningStrategy.CONSERVATIVE, TuningStrategy.MODERATE, 
                     TuningStrategy.AGGRESSIVE, TuningStrategy.ADAPTIVE]
        
        for strategy in strategies:
            tuner.set_tuning_strategy(strategy)
            status = tuner.get_tuning_status()
            assert status['current_strategy'] == strategy.value
        
        # Test threshold updates
        tuner.update_thresholds({
            'execution_time_critical': 3.0,
            'cache_hit_rate_minimum': 0.5
        })
        
        # Verify thresholds were updated
        assert tuner.thresholds['execution_time_critical'] == 3.0
        assert tuner.thresholds['cache_hit_rate_minimum'] == 0.5
        
        logger.info("✅ Tuning recommendations tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tuning recommendations test failed: {e}")
        return False


def test_integration_with_existing_system():
    """Test integration with existing system components"""
    try:
        # Test integration with advanced cache service
        from app.services.advanced_cache_service import advanced_cache_service
        from app.services.retrieval_analytics import get_retrieval_analytics
        
        analytics = get_retrieval_analytics()
        
        # Test integration with batch processor
        from app.services.batch_processor import get_batch_processor
        batch_processor = get_batch_processor()
        
        # Test integration with enhanced retrieval config
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        config = get_enhanced_retrieval_config()
        
        # Verify all components are accessible
        assert analytics is not None
        assert batch_processor is not None
        assert config is not None
        assert advanced_cache_service is not None
        
        logger.info("✅ Integration with existing system tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration with existing system test failed: {e}")
        return False


def test_auto_tuning_function():
    """Test the auto_tune_performance convenience function"""
    try:
        from app.services.performance_tuner import auto_tune_performance
        
        # Test auto-tuning function
        results = auto_tune_performance()
        assert isinstance(results, list)
        
        logger.info("✅ Auto-tuning function tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Auto-tuning function test failed: {e}")
        return False


def main():
    """Run all Phase 5 tests"""
    logger.info("🚀 Starting Phase 5: Advanced Analytics and Monitoring Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Retrieval Analytics", test_retrieval_analytics),
        ("Performance Tuner", test_performance_tuner),
        ("Analytics Configuration", test_analytics_configuration),
        ("Performance Monitoring Integration", test_performance_monitoring_integration),
        ("Analytics Export", test_analytics_export),
        ("Tuning Recommendations", test_tuning_recommendations),
        ("Integration with Existing System", test_integration_with_existing_system),
        ("Auto-tuning Function", test_auto_tuning_function)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Phase 5 tests passed! Analytics and monitoring system is ready.")
        return True
    else:
        logger.error(f"⚠️ {total - passed} tests failed. Please review the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 