#!/usr/bin/env python3
"""
Test script to verify and fix the "Bani" query issue
Tests the complete query service pipeline to identify where the failure occurs
"""

import os
import sys
import logging
import time
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_query_service_direct():
    """Test the query service directly with cross-category disabled"""
    print("=== Testing Query Service Direct (Cross-Category Disabled) ===")
    try:
        from app.services.query_service import query_category
        
        # Test with cross-category disabled
        result = query_category(
            category='CANOPY',
            question='Bani',
            anti_hallucination_mode='strict',
            use_cross_category=False  # Disable cross-category search
        )
        
        print(f"Query result: {result}")
        print(f"Answer: {result.get('answer', 'No answer')}")
        print(f"Sources count: {len(result.get('sources', []))}")
        print(f"Processing time: {result.get('processing_time', 0)}")
        
        return result
        
    except Exception as e:
        print(f"Error in direct query service test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_query_service_cross_category():
    """Test the query service with cross-category enabled"""
    print("\n=== Testing Query Service with Cross-Category Enabled ===")
    try:
        from app.services.query_service import query_category
        
        # Test with cross-category enabled
        result = query_category(
            category='CANOPY',
            question='Bani',
            anti_hallucination_mode='strict',
            use_cross_category=True  # Enable cross-category search
        )
        
        print(f"Query result: {result}")
        print(f"Answer: {result.get('answer', 'No answer')}")
        print(f"Sources count: {len(result.get('sources', []))}")
        print(f"Processing time: {result.get('processing_time', 0)}")
        
        return result
        
    except Exception as e:
        print(f"Error in cross-category query service test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_cross_category_retriever_direct():
    """Test the cross-category retriever directly"""
    print("\n=== Testing Cross-Category Retriever Direct ===")
    try:
        from app.services.cross_category_retriever import retrieve_cross_category
        
        user_context = {
            'user_id': 1,
            'session_id': 'test_session',
            'device_fingerprint': 'test_device',
            'recent_categories': ['CANOPY'],
            'preferred_categories': ['CANOPY']
        }
        
        start_time = time.time()
        result = retrieve_cross_category(
            query='Bani',
            user_context=user_context,
            max_total_documents=10
        )
        execution_time = time.time() - start_time
        
        print(f"Cross-category retrieval completed in {execution_time:.3f}s")
        print(f"Total documents retrieved: {result.total_documents_retrieved}")
        print(f"Documents per category: {result.documents_per_category}")
        print(f"Routing strategy: {result.routing_result.routing_strategy}")
        
        for i, doc in enumerate(result.documents[:5]):
            category = doc.metadata.get('category', 'Unknown')
            source = doc.metadata.get('source', 'Unknown')
            print(f"  {i+1}. Category: {category}, Source: {source}")
            
        return result
        
    except Exception as e:
        print(f"Error in cross-category retriever test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_vector_db_singleton():
    """Test the vector database singleton pattern"""
    print("\n=== Testing Vector Database Singleton ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        
        # Get multiple instances to test singleton
        db1 = get_unified_vector_db()
        db2 = get_unified_vector_db()
        
        print(f"DB1 ID: {id(db1)}")
        print(f"DB2 ID: {id(db2)}")
        print(f"Singleton working: {db1 is db2}")
        
        # Test basic search
        result = db1.similarity_search_with_score('Bani', category='CANOPY', k=5)
        print(f"Direct search found {len(result)} documents")
        
        return db1 is db2
        
    except Exception as e:
        print(f"Error in vector DB singleton test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_config():
    """Test query configuration"""
    print("\n=== Testing Query Configuration ===")
    try:
        from config.settings.query_config import get_query_config
        
        config = get_query_config()
        print(f"Retrieval K: {config.retrieval_k}")
        print(f"Max documents: {config.max_documents}")
        print(f"Min documents: {config.min_documents}")
        print(f"Relevance threshold: {config.relevance_threshold}")
        print(f"Enable cross-category search: {config.enable_cross_category_search}")
        
        return config
        
    except Exception as e:
        print(f"Error in query config test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_enhanced_retrieval_config():
    """Test enhanced retrieval configuration"""
    print("\n=== Testing Enhanced Retrieval Configuration ===")
    try:
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        print(f"Enable enhanced retrieval: {config.enable_enhanced_retrieval}")
        print(f"Enable performance monitoring: {config.enable_performance_monitoring}")
        
        if config.query_expansion:
            print(f"Query expansion enabled: {config.query_expansion.enable_entity_extraction}")
            print(f"Expansion confidence threshold: {config.query_expansion.expansion_confidence_threshold}")
        
        if config.result_diversification:
            print(f"Result diversification enabled: {config.result_diversification.enable_maximal_marginal_relevance}")
            print(f"Default ranking strategy: {config.result_diversification.default_ranking_strategy}")
        
        return config
        
    except Exception as e:
        print(f"Error in enhanced retrieval config test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_database_connectivity():
    """Test database connectivity and basic operations"""
    print("\n=== Testing Database Connectivity ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        
        db = get_unified_vector_db()
        
        # Test collection stats
        stats = db.get_collection_stats()
        print(f"Database stats: {stats}")
        
        # Test basic search
        result = db.similarity_search('test', k=1)
        print(f"Basic search test: {len(result)} documents found")
        
        return True
        
    except Exception as e:
        print(f"Error in database connectivity test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Starting comprehensive test of 'Bani' query issue...")
    print(f"Test started at: {datetime.now()}")
    
    # Test database connectivity first
    if not test_database_connectivity():
        print("❌ Database connectivity test failed - stopping tests")
        return
    
    # Test configurations
    query_config = test_query_config()
    enhanced_config = test_enhanced_retrieval_config()
    
    # Test vector DB singleton
    singleton_working = test_vector_db_singleton()
    
    # Test cross-category retriever directly
    cross_category_result = test_cross_category_retriever_direct()
    
    # Test query service with cross-category disabled
    direct_result = test_query_service_direct()
    
    # Test query service with cross-category enabled
    cross_category_query_result = test_query_service_cross_category()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Database connectivity: {'✅' if test_database_connectivity() else '❌'}")
    print(f"Query config loaded: {'✅' if query_config else '❌'}")
    print(f"Enhanced config loaded: {'✅' if enhanced_config else '❌'}")
    print(f"Vector DB singleton: {'✅' if singleton_working else '❌'}")
    print(f"Cross-category retriever: {'✅' if cross_category_result else '❌'}")
    print(f"Direct query service: {'✅' if direct_result else '❌'}")
    print(f"Cross-category query service: {'✅' if cross_category_query_result else '❌'}")
    
    # Analyze results
    if direct_result and 'I don\'t have any information' not in direct_result.get('answer', ''):
        print("\n✅ Direct query service is working correctly")
    else:
        print("\n❌ Direct query service is still failing")
    
    if cross_category_query_result and 'I don\'t have any information' not in cross_category_query_result.get('answer', ''):
        print("✅ Cross-category query service is working correctly")
    else:
        print("❌ Cross-category query service is still failing")
    
    print(f"\nTest completed at: {datetime.now()}")

if __name__ == "__main__":
    main() 