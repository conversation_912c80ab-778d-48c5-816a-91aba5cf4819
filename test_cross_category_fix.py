#!/usr/bin/env python3
"""
Test script to verify cross-category retrieval fix
"""

import logging
logging.basicConfig(level=logging.INFO)

def test_cross_category_retrieval():
    """Test cross-category retrieval for 'Bani'"""
    print("=== Testing Cross-Category Retrieval Fix ===")
    try:
        from app.services.cross_category_retriever import retrieve_cross_category
        
        user_context = {
            'user_id': 1,
            'session_id': 'test',
            'device_fingerprint': 'test',
            'recent_categories': ['RISE'],
            'preferred_categories': ['RISE']
        }
        
        result = retrieve_cross_category(
            query='Bani',
            user_context=user_context,
            max_total_documents=10
        )
        
        print(f"✅ Cross-category retrieval successful!")
        print(f"Found {len(result.documents)} documents")
        print(f"Categories: {result.documents_per_category}")
        print(f"Routing strategy: {result.routing_result.routing_strategy}")
        
        for i, doc in enumerate(result.documents[:3]):
            category = doc.metadata.get('category', 'Unknown')
            source = doc.metadata.get('source', 'Unknown')
            print(f"  {i+1}. Category: {category}, Source: {source}")
            
    except Exception as e:
        print(f"❌ Error in cross-category retrieval: {e}")
        import traceback
        traceback.print_exc()

def test_query_service():
    """Test the query service with cross-category search"""
    print("\n=== Testing Query Service with Cross-Category ===")
    try:
        from app.services.query_service import query_category
        
        response = query_category(
            category='RISE',
            question='Bani',
            use_cross_category=True,
            session_id='test',
            device_fingerprint='test'
        )
        
        print(f"✅ Query service successful!")
        print(f"Answer: {response.get('answer', 'No answer')[:100]}...")
        print(f"Sources: {len(response.get('sources', []))}")
        
        if response.get('sources'):
            print("Sources found:")
            for i, source in enumerate(response.get('sources', [])[:3]):
                print(f"  {i+1}. {source}")
        else:
            print("❌ No sources found")
            
    except Exception as e:
        print(f"❌ Error in query service: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cross_category_retrieval()
    test_query_service() 