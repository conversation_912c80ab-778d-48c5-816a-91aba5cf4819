#!/usr/bin/env python3
"""
Debug script to test search functionality for "Bani" across categories
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_direct_search():
    """Test direct search in unified vector database"""
    print("=== Testing Direct Search ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        db = get_unified_vector_db()
        result = db.similarity_search_with_score('Bani', k=10)
        print(f"Found {len(result)} documents for 'Bani'")
        
        for i, (doc, score) in enumerate(result[:5]):
            category = doc.metadata.get('category', 'Unknown')
            source = doc.metadata.get('source', 'Unknown')
            print(f"  {i+1}. Category: {category}, Score: {score:.3f}, Source: {source}")
    except Exception as e:
        print(f"Error in direct search: {e}")

def test_individual_category_search():
    """Test search in individual categories"""
    print("\n=== Testing Individual Category Search ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        db = get_unified_vector_db()
        
        for category in ['CANOPY', 'RISE', 'MANUAL']:
            print(f"\nSearching in {category} category:")
            try:
                result = db.similarity_search_with_score('Bani', k=5, filter={'category': category})
                print(f"  Found {len(result)} documents")
                for i, (doc, score) in enumerate(result[:3]):
                    doc_category = doc.metadata.get('category', 'Unknown')
                    source = doc.metadata.get('source', 'Unknown')
                    print(f"    {i+1}. Category: {doc_category}, Score: {score:.3f}, Source: {source}")
            except Exception as e:
                print(f"  Error: {e}")
    except Exception as e:
        print(f"Error in individual category search: {e}")

def test_cross_category_retrieval():
    """Test cross-category retrieval"""
    print("\n=== Testing Cross-Category Retrieval ===")
    try:
        from app.services.cross_category_retriever import retrieve_cross_category
        
        user_context = {
            'user_id': 1,
            'session_id': 'test',
            'device_fingerprint': 'test',
            'recent_categories': ['RISE'],
            'preferred_categories': ['RISE']
        }
        
        cross_category_result = retrieve_cross_category(
            query='Bani',
            user_context=user_context,
            max_total_documents=10
        )
        
        print(f"Total documents retrieved: {cross_category_result.total_documents_retrieved}")
        print(f"Documents per category: {cross_category_result.documents_per_category}")
        print(f"Routing strategy: {cross_category_result.routing_result.routing_strategy}")
        
        for i, doc in enumerate(cross_category_result.documents[:5]):
            category = doc.metadata.get('category', 'Unknown')
            source = doc.metadata.get('source', 'Unknown')
            print(f"  {i+1}. Category: {category}, Source: {source}")
            
    except Exception as e:
        print(f"Error in cross-category retrieval: {e}")

def test_database_stats():
    """Test database statistics"""
    print("\n=== Testing Database Statistics ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        db = get_unified_vector_db()
        stats = db.get_collection_stats()
        print(f"Total documents: {stats.get('total_documents', 'Unknown')}")
        print(f"Categories: {stats.get('categories', 'Unknown')}")
    except Exception as e:
        print(f"Error getting database stats: {e}")

def test_raw_chromadb_search():
    """Test raw ChromaDB search to check metadata"""
    print("\n=== Testing Raw ChromaDB Search ===")
    try:
        import chromadb
        client = chromadb.PersistentClient(path="./data/unified_chroma")
        collection = client.get_collection(name="unified_collection")
        
        # Get some documents to check metadata
        results = collection.get(limit=10)
        print(f"Sample documents metadata:")
        for i, metadata in enumerate(results['metadatas'][:5]):
            if metadata:
                category = metadata.get('category', 'Unknown')
                source = metadata.get('source', 'Unknown')
                print(f"  {i+1}. Category: {category}, Source: {source}")
        
        # Test search with category filter
        print(f"\nTesting search with category filter:")
        for category in ['CANOPY', 'RISE']:
            try:
                results = collection.query(
                    query_texts=['Bani'],
                    n_results=5,
                    where={'category': category}
                )
                print(f"  {category}: Found {len(results['documents'][0])} documents")
                for j, doc in enumerate(results['documents'][0][:3]):
                    print(f"    {j+1}. {doc[:100]}...")
            except Exception as e:
                print(f"  {category}: Error - {e}")
                
    except Exception as e:
        print(f"Error in raw ChromaDB search: {e}")

def test_embedding_model_info():
    """Test embedding model information"""
    print("\n=== Testing Embedding Model Info ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        db = get_unified_vector_db()
        
        # Test embedding function
        embed_fn = db._get_embedding_function()
        test_text = "test"
        embedding = embed_fn.embed_query(test_text)
        print(f"Embedding model: {db.embedding_model}")
        print(f"Embedding dimension: {len(embedding)}")
        
        # Check collection info
        import chromadb
        client = chromadb.PersistentClient(path="./data/unified_chroma")
        collection = client.get_collection(name="unified_collection")
        
        # Get collection info
        print(f"Collection name: {collection.name}")
        print(f"Collection count: {collection.count()}")
        
        # Try to get embedding dimension from collection
        try:
            # This might not be available in all ChromaDB versions
            print(f"Collection metadata: {collection.metadata}")
        except:
            print("Collection metadata not available")
            
    except Exception as e:
        print(f"Error in embedding model info: {e}")

def test_filter_debug():
    """Test filter debugging"""
    print("\n=== Testing Filter Debug ===")
    try:
        from app.services.unified_vector_db import get_unified_vector_db
        db = get_unified_vector_db()
        
        # Test with explicit filter
        print("Testing with explicit filter:")
        try:
            result = db.similarity_search_with_score('Bani', k=5, filter={'category': 'CANOPY'})
            print(f"  Found {len(result)} documents")
            for i, (doc, score) in enumerate(result[:3]):
                category = doc.metadata.get('category', 'Unknown')
                source = doc.metadata.get('source', 'Unknown')
                print(f"    {i+1}. Category: {category}, Score: {score:.3f}, Source: {source}")
        except Exception as e:
            print(f"  Error: {e}")
        
        # Test with category parameter
        print("\nTesting with category parameter:")
        try:
            result = db.similarity_search_with_score('Bani', category='CANOPY', k=5)
            print(f"  Found {len(result)} documents")
            for i, (doc, score) in enumerate(result[:3]):
                category = doc.metadata.get('category', 'Unknown')
                source = doc.metadata.get('source', 'Unknown')
                print(f"    {i+1}. Category: {category}, Score: {score:.3f}, Source: {source}")
        except Exception as e:
            print(f"  Error: {e}")
            
    except Exception as e:
        print(f"Error in filter debug: {e}")

if __name__ == "__main__":
    test_direct_search()
    test_individual_category_search()
    test_raw_chromadb_search()
    test_embedding_model_info()
    test_filter_debug()
    test_cross_category_retrieval()
    test_database_stats() 