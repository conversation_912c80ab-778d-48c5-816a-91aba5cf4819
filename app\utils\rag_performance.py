"""
RAG Pipeline Performance Monitoring
Enhanced metrics for retrieval-augmented generation optimizations
"""

import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict
import threading

logger = logging.getLogger(__name__)

@dataclass
class RAGMetric:
    """Metric for RAG pipeline operations"""
    operation_type: str  # 'query', 'retrieval', 'scoring', 'caching'
    category: str
    execution_time: float
    cache_hit: bool = False
    adaptive_k: Optional[int] = None
    base_k: Optional[int] = None
    parallel_workers: Optional[int] = None
    document_count: int = 0
    relevance_scores: List[float] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()
        if self.relevance_scores is None:
            self.relevance_scores = []

class RAGPerformanceMonitor:
    """Enhanced performance monitoring for RAG pipeline"""
    
    def __init__(self):
        self.metrics: List[RAGMetric] = []
        self.cache_stats = {
            'query_hits': 0,
            'query_misses': 0,
            'embedding_hits': 0,
            'embedding_misses': 0
        }
        self.adaptive_retrieval_stats = defaultdict(list)
        self.parallel_processing_stats = []
        self.lock = threading.Lock()
    
    def record_query_metric(self, category: str, execution_time: float, 
                           cache_hit: bool = False, adaptive_k: int = None, 
                           base_k: int = None, document_count: int = 0,
                           relevance_scores: List[float] = None):
        """Record query processing metrics"""
        with self.lock:
            metric = RAGMetric(
                operation_type='query',
                category=category,
                execution_time=execution_time,
                cache_hit=cache_hit,
                adaptive_k=adaptive_k,
                base_k=base_k,
                document_count=document_count,
                relevance_scores=relevance_scores or []
            )
            self.metrics.append(metric)
            
            # Update cache stats
            if cache_hit:
                self.cache_stats['query_hits'] += 1
            else:
                self.cache_stats['query_misses'] += 1
            
            # Update adaptive retrieval stats
            if adaptive_k and base_k:
                self.adaptive_retrieval_stats[category].append({
                    'adaptive_k': adaptive_k,
                    'base_k': base_k,
                    'execution_time': execution_time,
                    'document_count': document_count
                })
    
    def record_parallel_scoring(self, execution_time: float, workers: int, 
                               document_count: int, category: str):
        """Record parallel document scoring metrics"""
        with self.lock:
            metric = RAGMetric(
                operation_type='scoring',
                category=category,
                execution_time=execution_time,
                parallel_workers=workers,
                document_count=document_count
            )
            self.metrics.append(metric)
            
            self.parallel_processing_stats.append({
                'execution_time': execution_time,
                'workers': workers,
                'document_count': document_count,
                'category': category,
                'timestamp': datetime.now().isoformat()
            })
    
    def record_cache_hit(self, cache_type: str):
        """Record cache hit"""
        with self.lock:
            if cache_type == 'query':
                self.cache_stats['query_hits'] += 1
            elif cache_type == 'embedding':
                self.cache_stats['embedding_hits'] += 1
    
    def record_cache_miss(self, cache_type: str):
        """Record cache miss"""
        with self.lock:
            if cache_type == 'query':
                self.cache_stats['query_misses'] += 1
            elif cache_type == 'embedding':
                self.cache_stats['embedding_misses'] += 1
    
    def get_cache_hit_rate(self, cache_type: str = 'query') -> float:
        """Get cache hit rate for specified cache type"""
        with self.lock:
            if cache_type == 'query':
                total = self.cache_stats['query_hits'] + self.cache_stats['query_misses']
                return self.cache_stats['query_hits'] / total if total > 0 else 0
            elif cache_type == 'embedding':
                total = self.cache_stats['embedding_hits'] + self.cache_stats['embedding_misses']
                return self.cache_stats['embedding_hits'] / total if total > 0 else 0
            return 0
    
    def get_adaptive_retrieval_performance(self) -> Dict[str, Any]:
        """Get adaptive retrieval performance statistics"""
        with self.lock:
            stats = {}
            for category, metrics in self.adaptive_retrieval_stats.items():
                if not metrics:
                    continue
                
                avg_time = sum(m['execution_time'] for m in metrics) / len(metrics)
                avg_adaptive_k = sum(m['adaptive_k'] for m in metrics) / len(metrics)
                avg_base_k = sum(m['base_k'] for m in metrics) / len(metrics)
                avg_docs = sum(m['document_count'] for m in metrics) / len(metrics)
                
                stats[category] = {
                    'query_count': len(metrics),
                    'avg_execution_time': avg_time,
                    'avg_adaptive_k': avg_adaptive_k,
                    'avg_base_k': avg_base_k,
                    'avg_document_count': avg_docs,
                    'k_adjustment_ratio': avg_adaptive_k / avg_base_k if avg_base_k > 0 else 1
                }
            
            return stats
    
    def get_parallel_processing_performance(self) -> Dict[str, Any]:
        """Get parallel processing performance statistics"""
        with self.lock:
            if not self.parallel_processing_stats:
                return {}
            
            total_time = sum(s['execution_time'] for s in self.parallel_processing_stats)
            avg_time = total_time / len(self.parallel_processing_stats)
            avg_workers = sum(s['workers'] for s in self.parallel_processing_stats) / len(self.parallel_processing_stats)
            avg_docs = sum(s['document_count'] for s in self.parallel_processing_stats) / len(self.parallel_processing_stats)
            
            return {
                'total_operations': len(self.parallel_processing_stats),
                'avg_execution_time': avg_time,
                'avg_workers': avg_workers,
                'avg_document_count': avg_docs,
                'total_time_saved': total_time  # Estimate based on parallel vs sequential
            }
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive RAG performance statistics"""
        with self.lock:
            return {
                'cache_performance': {
                    'query_hit_rate': self.get_cache_hit_rate('query'),
                    'embedding_hit_rate': self.get_cache_hit_rate('embedding'),
                    'total_query_hits': self.cache_stats['query_hits'],
                    'total_query_misses': self.cache_stats['query_misses'],
                    'total_embedding_hits': self.cache_stats['embedding_hits'],
                    'total_embedding_misses': self.cache_stats['embedding_misses']
                },
                'adaptive_retrieval': self.get_adaptive_retrieval_performance(),
                'parallel_processing': self.get_parallel_processing_performance(),
                'total_metrics': len(self.metrics),
                'monitoring_start': self.metrics[0].timestamp if self.metrics else None,
                'last_update': self.metrics[-1].timestamp if self.metrics else None
            }
    
    def reset_stats(self):
        """Reset all performance statistics"""
        with self.lock:
            self.metrics.clear()
            self.cache_stats = {
                'query_hits': 0,
                'query_misses': 0,
                'embedding_hits': 0,
                'embedding_misses': 0
            }
            self.adaptive_retrieval_stats.clear()
            self.parallel_processing_stats.clear()
            logger.info("RAG performance statistics reset")

# Global RAG performance monitor
_rag_monitor: Optional[RAGPerformanceMonitor] = None

def get_rag_monitor() -> RAGPerformanceMonitor:
    """Get the global RAG performance monitor instance"""
    global _rag_monitor
    if _rag_monitor is None:
        _rag_monitor = RAGPerformanceMonitor()
    return _rag_monitor

def monitor_rag_operation(operation_type: str):
    """Decorator for monitoring RAG operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            monitor = get_rag_monitor()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Extract relevant information from args/kwargs for monitoring
                category = kwargs.get('category') or (args[0] if args else 'unknown')
                
                if operation_type == 'query':
                    # Record query metrics
                    cache_hit = getattr(result, 'metadata', {}).get('cached', False) if isinstance(result, dict) else False
                    document_count = getattr(result, 'metadata', {}).get('document_count', 0) if isinstance(result, dict) else 0
                    
                    monitor.record_query_metric(
                        category=str(category),
                        execution_time=execution_time,
                        cache_hit=cache_hit,
                        document_count=document_count
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"RAG operation {operation_type} failed after {execution_time:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator


def track_hybrid_search_metrics(query: str, category: str, strategies_used: List[str], 
                               execution_time: float, total_documents: int, cache_hit_rate: float):
    """Track hybrid search performance metrics"""
    monitor = get_rag_monitor()
    
    # Record hybrid search specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type='hybrid_search',
            category=category,
            execution_time=execution_time,
            document_count=total_documents,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log hybrid search performance
        logger.info(f"Hybrid search completed in {execution_time:.3f}s with {len(strategies_used)} strategies")
        logger.info(f"Strategies used: {strategies_used}")
        logger.info(f"Documents retrieved: {total_documents}")
        logger.info(f"Cache hit rate: {cache_hit_rate:.2f}")

def track_batch_processing_metrics(job_type: str, total_items: int, successful_items: int,
                                  execution_time: float, throughput: float, errors: List[str]):
    """Track batch processing performance metrics"""
    monitor = get_rag_monitor()
    
    # Record batch processing specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'batch_{job_type}',
            category='batch_processing',
            execution_time=execution_time,
            document_count=total_items,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log batch processing performance
        logger.info(f"Batch {job_type} completed in {execution_time:.3f}s")
        logger.info(f"Processed {successful_items}/{total_items} items successfully")
        logger.info(f"Throughput: {throughput:.2f} items/second")
        if errors:
            logger.warning(f"Batch {job_type} had {len(errors)} errors")

def track_advanced_cache_metrics(cache_type: str, operation: str, execution_time: float,
                                cache_hits: int = 0, cache_misses: int = 0, 
                                memory_usage_mb: float = 0, predictive_hits: int = 0):
    """Track advanced cache performance metrics"""
    monitor = get_rag_monitor()
    
    # Record advanced cache specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'advanced_cache_{cache_type}',
            category='cache_operations',
            execution_time=execution_time,
            document_count=cache_hits + cache_misses,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log advanced cache performance
        logger.info(f"Advanced cache {operation} completed in {execution_time:.3f}s")
        logger.info(f"Cache hits: {cache_hits}, misses: {cache_misses}")
        logger.info(f"Memory usage: {memory_usage_mb:.2f}MB")
        logger.info(f"Predictive hits: {predictive_hits}")


def track_analytics_metrics(operation_type: str, execution_time: float, 
                           documents_processed: int = 0, cache_hits: int = 0, 
                           cache_misses: int = 0, relevance_scores: List[float] = None,
                           user_feedback: int = None, error_message: str = None):
    """Track analytics-specific performance metrics"""
    monitor = get_rag_monitor()
    
    # Record analytics specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'analytics_{operation_type}',
            category='analytics',
            execution_time=execution_time,
            document_count=documents_processed,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log analytics performance
        logger.info(f"Analytics {operation_type} completed in {execution_time:.3f}s")
        logger.info(f"Documents processed: {documents_processed}")
        logger.info(f"Cache hits: {cache_hits}, misses: {cache_misses}")
        if relevance_scores:
            avg_relevance = sum(relevance_scores) / len(relevance_scores)
            logger.info(f"Average relevance score: {avg_relevance:.3f}")
        if user_feedback:
            logger.info(f"User feedback: {user_feedback}/5")


def track_performance_tuning_metrics(tuning_action: str, execution_time: float,
                                   success: bool, impact_metrics: Dict[str, float],
                                   error_message: str = None):
    """Track performance tuning metrics"""
    monitor = get_rag_monitor()
    
    # Record performance tuning specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'tuning_{tuning_action}',
            category='performance_tuning',
            execution_time=execution_time,
            document_count=0,  # Not applicable for tuning
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log performance tuning results
        logger.info(f"Performance tuning {tuning_action} completed in {execution_time:.3f}s")
        logger.info(f"Success: {success}")
        if impact_metrics:
            for key, value in impact_metrics.items():
                logger.info(f"Impact - {key}: {value}")
        if error_message:
            logger.warning(f"Tuning error: {error_message}")


def track_retrieval_quality_metrics(query_id: str, precision_at_k: float, recall_at_k: float,
                                   f1_score_at_k: float, mrr: float, ndcg: float,
                                   avg_relevance_score: float, diversity_score: float,
                                   coverage_score: float):
    """Track retrieval quality metrics"""
    monitor = get_rag_monitor()
    
    # Record retrieval quality specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'quality_analysis_{query_id}',
            category='retrieval_quality',
            execution_time=0.0,  # Not applicable for quality metrics
            document_count=0,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log retrieval quality metrics
        logger.info(f"Retrieval quality analysis for query {query_id}")
        logger.info(f"Precision@k: {precision_at_k:.3f}, Recall@k: {recall_at_k:.3f}")
        logger.info(f"F1@k: {f1_score_at_k:.3f}, MRR: {mrr:.3f}, NDCG: {ndcg:.3f}")
        logger.info(f"Avg relevance: {avg_relevance_score:.3f}")
        logger.info(f"Diversity: {diversity_score:.3f}, Coverage: {coverage_score:.3f}")


def track_bottleneck_analysis_metrics(analysis_type: str, execution_time: float,
                                    bottlenecks_found: int, recommendations_generated: int,
                                    critical_issues: int = 0):
    """Track bottleneck analysis metrics"""
    monitor = get_rag_monitor()
    
    # Record bottleneck analysis specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'bottleneck_analysis_{analysis_type}',
            category='bottleneck_analysis',
            execution_time=execution_time,
            document_count=bottlenecks_found,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)
        
        # Log bottleneck analysis results
        logger.info(f"Bottleneck analysis {analysis_type} completed in {execution_time:.3f}s")
        logger.info(f"Bottlenecks found: {bottlenecks_found}")
        logger.info(f"Recommendations generated: {recommendations_generated}")
        if critical_issues > 0:
            logger.warning(f"Critical issues detected: {critical_issues}")


def track_query_expansion_metrics(operation_type: str, execution_time: float,
                                documents_processed: int = 0, expansions_generated: int = 0,
                                confidence_score: float = 0.0, error_message: str = None):
    """Track query expansion and understanding metrics"""
    monitor = get_rag_monitor()

    # Record query expansion specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'query_expansion_{operation_type}',
            category='query_expansion',
            execution_time=execution_time,
            document_count=documents_processed,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)

        # Log query expansion performance
        logger.info(f"Query expansion {operation_type} completed in {execution_time:.3f}s")
        logger.info(f"Documents processed: {documents_processed}")
        logger.info(f"Expansions generated: {expansions_generated}")
        if confidence_score > 0:
            logger.info(f"Confidence score: {confidence_score:.3f}")
        if error_message:
            logger.warning(f"Query expansion error: {error_message}")


def track_result_diversification_metrics(operation_type: str, execution_time: float,
                                       documents_processed: int = 0, diversity_score: float = 0.0,
                                       coverage_score: float = 0.0, ranking_strategy: str = None,
                                       error_message: str = None):
    """Track result diversification and ranking metrics"""
    monitor = get_rag_monitor()

    # Record result diversification specific metrics
    with monitor.lock:
        metric = RAGMetric(
            operation_type=f'result_diversification_{operation_type}',
            category='result_diversification',
            execution_time=execution_time,
            document_count=documents_processed,
            timestamp=datetime.now().isoformat()
        )
        monitor.metrics.append(metric)

        # Log result diversification performance
        logger.info(f"Result diversification {operation_type} completed in {execution_time:.3f}s")
        logger.info(f"Documents processed: {documents_processed}")
        logger.info(f"Diversity score: {diversity_score:.3f}")
        logger.info(f"Coverage score: {coverage_score:.3f}")
        if ranking_strategy:
            logger.info(f"Ranking strategy: {ranking_strategy}")
        if error_message:
            logger.warning(f"Result diversification error: {error_message}")
