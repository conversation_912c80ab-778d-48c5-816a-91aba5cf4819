#!/usr/bin/env python3
"""
Test script for hybrid search functionality

This script tests the hybrid search service and compares its performance
with the existing retrieval methods.
"""

import sys
import os
import time
import logging
from typing import List, Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_hybrid_search_imports():
    """Test that all hybrid search components can be imported."""
    try:
        from app.services.hybrid_search_service import (
            HybridSearchService, 
            SearchStrategy, 
            SearchResult, 
            HybridSearchResult,
            hybrid_search,
            get_hybrid_search_service
        )
        logger.info("✅ All hybrid search imports successful")
        return True
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False

def test_semantic_analyzer_integration():
    """Test integration with semantic analyzer."""
    try:
        from app.services.semantic_analyzer import analyze_query_semantics, QueryIntent
        from app.services.hybrid_search_service import hybrid_search, SearchStrategy
        
        # Test query analysis
        test_queries = [
            "What are the benefits of sustainable forestry?",
            "How to propagate dipterocarps?",
            "Climate change impact on Philippine forests",
            "Bamboo species in the Philippines"
        ]
        
        for query in test_queries:
            analysis = analyze_query_semantics(query)
            logger.info(f"Query: '{query}'")
            logger.info(f"  Intent: {analysis.complexity.intent.value}")
            logger.info(f"  Complexity: {analysis.complexity.complexity_level}")
            logger.info(f"  Domain keywords: {analysis.domain_keywords}")
            logger.info(f"  Suggested k: {analysis.complexity.suggested_k}")
        
        logger.info("✅ Semantic analyzer integration successful")
        return True
    except Exception as e:
        logger.error(f"❌ Semantic analyzer integration error: {e}")
        return False

def test_hybrid_search_strategies():
    """Test different search strategies."""
    try:
        from app.services.hybrid_search_service import SearchStrategy, get_hybrid_search_service
        
        service = get_hybrid_search_service()
        
        # Test strategy selection
        test_cases = [
            {
                "query": "What is sustainable forestry?",
                "expected_strategies": [SearchStrategy.SEMANTIC, SearchStrategy.KEYWORD]
            },
            {
                "query": "Dipterocarp propagation techniques",
                "expected_strategies": [SearchStrategy.SEMANTIC, SearchStrategy.KEYWORD, SearchStrategy.EXACT_MATCH]
            },
            {
                "query": "How does climate change affect forest ecosystems in Southeast Asia?",
                "expected_strategies": [SearchStrategy.SEMANTIC, SearchStrategy.KEYWORD, SearchStrategy.FUZZY]
            }
        ]
        
        for case in test_cases:
            query = case["query"]
            expected = case["expected_strategies"]
            
            # Test strategy selection
            strategies = service._select_strategies(query)
            logger.info(f"Query: '{query}'")
            logger.info(f"  Selected strategies: {[s.value for s in strategies]}")
            logger.info(f"  Expected strategies: {[s.value for s in expected]}")
            
            # Test weight calculation
            weights = service._calculate_weights(query, strategies=strategies)
            logger.info(f"  Strategy weights: {weights}")
        
        logger.info("✅ Strategy selection and weight calculation successful")
        return True
    except Exception as e:
        logger.error(f"❌ Strategy testing error: {e}")
        return False

def test_keyword_extraction():
    """Test keyword and phrase extraction."""
    try:
        from app.services.hybrid_search_service import get_hybrid_search_service
        
        service = get_hybrid_search_service()
        
        test_queries = [
            "Sustainable forestry practices in the Philippines",
            "Dipterocarp propagation using non-mist technique",
            "Climate change impact on forest biodiversity",
            "Bamboo species identification and management"
        ]
        
        for query in test_queries:
            keywords = service._extract_keywords(query)
            phrases = service._extract_phrases(query)
            
            logger.info(f"Query: '{query}'")
            logger.info(f"  Keywords: {keywords}")
            logger.info(f"  Phrases: {phrases}")
        
        logger.info("✅ Keyword and phrase extraction successful")
        return True
    except Exception as e:
        logger.error(f"❌ Keyword extraction error: {e}")
        return False

def test_hybrid_search_integration():
    """Test hybrid search integration with the main query service."""
    try:
        from app.services.query_service import query_category
        from app.services.semantic_analyzer import analyze_query_semantics
        
        # Test queries that should trigger hybrid search
        test_queries = [
            {
                "query": "What are the main bamboo species found in the Philippines?",
                "category": "MANUAL",
                "description": "Factual query with specific terms"
            },
            {
                "query": "How to propagate dipterocarps using non-mist technique?",
                "category": "MANUAL", 
                "description": "Specific technical query"
            },
            {
                "query": "Climate change effects on Philippine forest ecosystems",
                "category": "CANOPY",
                "description": "Complex analytical query"
            }
        ]
        
        for test_case in test_queries:
            query = test_case["query"]
            category = test_case["category"]
            description = test_case["description"]
            
            logger.info(f"\n--- Testing: {description} ---")
            logger.info(f"Query: '{query}'")
            logger.info(f"Category: {category}")
            
            # Analyze query first
            analysis = analyze_query_semantics(query)
            logger.info(f"Query analysis:")
            logger.info(f"  Intent: {analysis.complexity.intent.value}")
            logger.info(f"  Complexity: {analysis.complexity.complexity_level}")
            logger.info(f"  Word count: {analysis.complexity.word_count}")
            logger.info(f"  Domain keywords: {analysis.domain_keywords}")
            
            # Determine if hybrid search should be used
            use_hybrid = (
                analysis.complexity.intent.value in ['FACTUAL', 'ANALYTICAL'] or
                analysis.complexity.word_count > 5 or
                len(analysis.domain_keywords) > 0
            )
            
            logger.info(f"Should use hybrid search: {use_hybrid}")
            
            # Note: We can't actually run the full query without a running database
            # This is a conceptual test to verify the logic
            
        logger.info("✅ Hybrid search integration logic successful")
        return True
    except Exception as e:
        logger.error(f"❌ Integration testing error: {e}")
        return False

def test_configuration():
    """Test hybrid search configuration."""
    try:
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        
        logger.info("Hybrid Search Configuration:")
        logger.info(f"  Default semantic weight: {config.hybrid_search.default_semantic_weight}")
        logger.info(f"  Default keyword weight: {config.hybrid_search.default_keyword_weight}")
        logger.info(f"  Default fuzzy weight: {config.hybrid_search.default_fuzzy_weight}")
        logger.info(f"  Default exact match weight: {config.hybrid_search.default_exact_match_weight}")
        logger.info(f"  Max keyword results: {config.hybrid_search.max_keyword_results}")
        logger.info(f"  Max fuzzy results: {config.hybrid_search.max_fuzzy_results}")
        logger.info(f"  Max exact match results: {config.hybrid_search.max_exact_match_results}")
        logger.info(f"  Enable strategy selection: {config.hybrid_search.enable_strategy_selection}")
        logger.info(f"  Enable weight adaptation: {config.hybrid_search.enable_weight_adaptation}")
        
        logger.info("✅ Configuration loading successful")
        return True
    except Exception as e:
        logger.error(f"❌ Configuration error: {e}")
        return False

def test_performance_monitoring():
    """Test performance monitoring integration."""
    try:
        from app.utils.rag_performance import track_hybrid_search_metrics
        
        # Test metrics tracking (conceptual)
        test_metrics = {
            "query": "Test query",
            "category": "TEST",
            "strategies_used": ["semantic", "keyword"],
            "execution_time": 0.5,
            "total_documents": 10,
            "cache_hit_rate": 0.3
        }
        
        logger.info("Testing hybrid search metrics tracking...")
        logger.info(f"  Query: {test_metrics['query']}")
        logger.info(f"  Category: {test_metrics['category']}")
        logger.info(f"  Strategies: {test_metrics['strategies_used']}")
        logger.info(f"  Execution time: {test_metrics['execution_time']}s")
        logger.info(f"  Documents: {test_metrics['total_documents']}")
        logger.info(f"  Cache hit rate: {test_metrics['cache_hit_rate']}")
        
        logger.info("✅ Performance monitoring integration successful")
        return True
    except Exception as e:
        logger.error(f"❌ Performance monitoring error: {e}")
        return False

def main():
    """Run all hybrid search tests."""
    logger.info("🚀 Starting Hybrid Search Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Import Tests", test_hybrid_search_imports),
        ("Semantic Analyzer Integration", test_semantic_analyzer_integration),
        ("Strategy Testing", test_hybrid_search_strategies),
        ("Keyword Extraction", test_keyword_extraction),
        ("Configuration", test_configuration),
        ("Performance Monitoring", test_performance_monitoring),
        ("Integration Logic", test_hybrid_search_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Results Summary")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Hybrid search implementation is ready.")
    else:
        logger.warning(f"⚠️  {total - passed} tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 