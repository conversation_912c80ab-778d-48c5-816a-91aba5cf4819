"""
Enhanced Retrieval Configuration
Centralized configuration for the enhanced retrieval system including semantic analysis,
multi-stage retrieval, and enhanced scoring parameters.
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Any

# Environment variable defaults
ENABLE_ENHANCED_RETRIEVAL = os.getenv('ENABLE_ENHANCED_RETRIEVAL', 'true').lower() == 'true'
ENABLE_PERFORMANCE_MONITORING = os.getenv('ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true'
ENABLE_DETAILED_LOGGING = os.getenv('ENABLE_DETAILED_LOGGING', 'false').lower() == 'true'
ENABLE_QUERY_EXPANSION = os.getenv('ENABLE_QUERY_EXPANSION', 'true').lower() == 'true'
ENABLE_RESULT_DIVERSIFICATION = os.getenv('ENABLE_RESULT_DIVERSIFICATION', 'true').lower() == 'true'

@dataclass
class SemanticAnalyzerConfig:
    """Configuration for semantic analyzer"""
    
    # Query intent classification
    enable_intent_classification: bool = True
    intent_confidence_threshold: float = 0.6
    
    # Complexity analysis
    enable_complexity_analysis: bool = True
    complexity_weights: Dict[str, float] = None
    
    # Domain keyword extraction
    enable_domain_extraction: bool = True
    domain_boost_enabled: bool = True
    
    # Query expansion
    enable_query_expansion: bool = True
    max_expanded_terms: int = 10
    
    def __post_init__(self):
        if self.complexity_weights is None:
            self.complexity_weights = {
                'semantic': 0.5,
                'word_count': 0.3,
                'domain_terms': 0.2
            }

@dataclass
class EnhancedScoringConfig:
    """Configuration for enhanced relevance scoring"""
    
    # Scoring weights
    scoring_weights: Dict[str, float] = None
    
    # Domain-specific boosts
    domain_boosts: Dict[str, float] = None
    
    # Temporal decay settings
    temporal_decay_enabled: bool = True
    temporal_decay_rates: Dict[str, float] = None
    
    # Contextual indicators
    contextual_indicators: Dict[str, List[str]] = None
    
    # Semantic similarity
    semantic_similarity_threshold: float = 0.3
    max_content_length_for_embedding: int = 1000
    
    def __post_init__(self):
        if self.scoring_weights is None:
            self.scoring_weights = {
                'semantic': 0.35,
                'keyword': 0.25,
                'contextual': 0.20,
                'temporal': 0.10,
                'domain': 0.10
            }
        
        if self.domain_boosts is None:
            self.domain_boosts = {
                'forestry': 1.2,
                'environment': 1.15,
                'research': 1.1,
                'agriculture': 1.1,
                'wildlife': 1.1,
                'water': 1.1,
                'climate': 1.15
            }
        
        if self.temporal_decay_rates is None:
            self.temporal_decay_rates = {
                'recent': 1.0,      # 0-2 years
                'current': 0.9,     # 3-5 years
                'moderate': 0.8,    # 6-10 years
                'older': 0.6,       # 11-20 years
                'historical': 0.4   # 20+ years
            }
        
        if self.contextual_indicators is None:
            self.contextual_indicators = {
                'authority': ['peer-reviewed', 'journal', 'conference', 'university', 'institute'],
                'methodology': ['method', 'methodology', 'procedure', 'technique', 'approach'],
                'completeness': ['comprehensive', 'detailed', 'complete', 'thorough', 'extensive'],
                'relevance': ['relevant', 'related', 'applicable', 'pertinent', 'appropriate']
            }

@dataclass
class MultiStageRetrieverConfig:
    """Configuration for multi-stage retrieval"""
    
    # Stage 1: Broad retrieval
    stage1_broad_k_multiplier: float = 2.0
    stage1_min_k: int = 8
    stage1_max_k: int = 30
    
    # Stage 2: Semantic re-ranking
    stage2_rerank_top_n: int = 20
    semantic_threshold: float = 0.3
    
    # Stage 3: Contextual filtering
    stage3_final_top_n: int = 8
    diversity_threshold: float = 0.7
    
    # Performance settings
    enable_parallel_processing: bool = True
    max_parallel_workers: int = 4
    
    # Fallback settings
    enable_fallback_to_simple_retrieval: bool = True
    fallback_k: int = 12


@dataclass
class HybridSearchConfig:
    """Configuration for hybrid search"""
    
    # Strategy weights
    default_semantic_weight: float = 0.6
    default_keyword_weight: float = 0.25
    default_fuzzy_weight: float = 0.1
    default_exact_match_weight: float = 0.05
    
    # Result limits per strategy
    max_keyword_results: int = 20
    max_fuzzy_results: int = 15
    max_exact_match_results: int = 10
    
    # Strategy selection
    enable_strategy_selection: bool = True
    enable_weight_adaptation: bool = True
    
    # Search parameters
    min_keyword_length: int = 3
    fuzzy_match_threshold: float = 0.7
    
    # Performance settings
    enable_parallel_strategies: bool = True
    max_parallel_strategies: int = 3

@dataclass
class CrossCategoryConfig:
    """Configuration for cross-category retrieval"""
    
    # Routing settings
    enable_intelligent_routing: bool = True
    enable_dynamic_weighting: bool = True
    enable_user_context: bool = True
    
    # Category weighting factors
    keyword_weight_factor: float = 0.4
    semantic_weight_factor: float = 0.3
    statistics_weight_factor: float = 0.15
    context_weight_factor: float = 0.1
    intent_weight_factor: float = 0.05
    
    # Thresholds
    primary_category_threshold: float = 0.5
    secondary_category_threshold: float = 0.2
    cross_category_threshold_min: float = 0.1
    cross_category_threshold_max: float = 0.7
    
    # Document allocation
    min_documents_per_category: int = 2
    max_documents_per_category: int = 15
    max_total_documents: int = 30
    
    # Performance settings
    enable_parallel_category_search: bool = True
    max_parallel_workers: int = 3
    cache_ttl: int = 1800  # 30 minutes
    
    # Routing strategies
    routing_strategies: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.routing_strategies is None:
            self.routing_strategies = {
                'comprehensive_search': {
                    'description': 'Search across all relevant categories',
                    'use_all_categories': True,
                    'weight_distribution': 'balanced'
                },
                'balanced_search': {
                    'description': 'Focus on primary categories with secondary support',
                    'primary_ratio': 0.7,
                    'secondary_ratio': 0.3
                },
                'focused_search': {
                    'description': 'Focus on the most relevant category',
                    'max_secondary_categories': 1,
                    'min_primary_weight': 0.6
                },
                'research_focused': {
                    'description': 'Optimized for research queries',
                    'prefer_categories': ['RISE'],
                    'boost_analytical_intent': True
                },
                'manual_focused': {
                    'description': 'Optimized for procedural queries',
                    'prefer_categories': ['MANUAL'],
                    'boost_procedural_intent': True
                },
                'forestry_focused': {
                    'description': 'Optimized for forestry queries',
                    'prefer_categories': ['CANOPY'],
                    'boost_factual_intent': True
                }
            }

@dataclass
class AdvancedCacheConfig:
    """Configuration for advanced caching system"""
    
    # Memory management
    max_memory_mb: int = 512
    memory_cleanup_interval: int = 300  # 5 minutes
    eviction_strategy: str = 'intelligent'  # 'lru', 'intelligent', 'priority'
    
    # Predictive caching
    predictive_enabled: bool = True
    prediction_window_hours: int = 24
    warmup_queue_size: int = 100
    prediction_queue_size: int = 1000
    
    # Access pattern analysis
    pattern_analysis_enabled: bool = True
    time_pattern_weight: float = 0.6
    day_pattern_weight: float = 0.4
    min_access_threshold: int = 2
    
    # Cache warming
    warming_enabled: bool = True
    warming_batch_size: int = 50
    warming_interval: int = 600  # 10 minutes
    
    # Performance settings
    default_ttl: int = 3600  # 1 hour
    high_priority_ttl: int = 7200  # 2 hours
    low_priority_ttl: int = 1800  # 30 minutes

@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing system"""
    
    # Worker configuration
    max_workers: int = 8
    max_queue_size: int = 1000
    process_pool_workers: int = 4
    
    # Job priorities
    embedding_priority: int = 1
    document_processing_priority: int = 2
    vector_search_priority: int = 3
    cache_warming_priority: int = 4
    
    # Timeouts
    default_timeout: int = 300  # 5 minutes
    embedding_timeout: int = 600  # 10 minutes
    document_timeout: int = 900  # 15 minutes
    
    # Batch sizes
    max_batch_size: int = 100
    optimal_batch_size: int = 50
    min_batch_size: int = 10
    
    # Performance settings
    parallel_processing: bool = True
    async_processing: bool = True
    result_caching: bool = True

@dataclass
class AnalyticsConfig:
    """Configuration for analytics system"""
    
    # Data retention
    max_history_size: int = 10000
    retention_days: int = 30
    cleanup_interval_hours: int = 24
    
    # Performance thresholds
    execution_time_warning_threshold: float = 2.0
    execution_time_critical_threshold: float = 5.0
    cache_hit_rate_minimum: float = 0.3
    cache_hit_rate_target: float = 0.7
    success_rate_minimum: float = 0.8
    success_rate_target: float = 0.95
    relevance_score_minimum: float = 0.6
    relevance_score_target: float = 0.8
    
    # Quality metrics
    enable_quality_metrics: bool = True
    quality_analysis_interval: int = 3600  # 1 hour
    enable_user_feedback: bool = True
    feedback_weight: float = 0.1
    
    # Export and reporting
    enable_analytics_export: bool = True
    export_format: str = 'json'
    report_generation_interval: int = 86400  # 24 hours


@dataclass
class PerformanceTuningConfig:
    """Configuration for performance tuning system"""
    
    # Tuning strategies
    default_strategy: str = 'adaptive'  # 'conservative', 'moderate', 'aggressive', 'adaptive'
    enable_auto_tuning: bool = True
    tuning_interval_minutes: int = 30
    
    # Tuning thresholds
    tuning_confidence_threshold: float = 0.6
    tuning_priority_threshold: int = 3
    max_tuning_actions_per_cycle: int = 5
    
    # Cache tuning
    cache_ttl_min: int = 300  # 5 minutes
    cache_ttl_max: int = 7200  # 2 hours
    cache_ttl_adjustment_factor: float = 1.5
    
    # Batch processing tuning
    batch_size_min: int = 10
    batch_size_max: int = 100
    batch_size_adjustment_factor: float = 1.5
    
    # Worker tuning
    worker_count_min: int = 2
    worker_count_max: int = 16
    worker_adjustment_step: int = 2
    
    # K-value tuning
    k_value_min: int = 3
    k_value_max: int = 20
    k_adjustment_factor: float = 1.2
    
    # Memory management
    memory_cleanup_threshold_mb: float = 400.0
    aggressive_cleanup_threshold_mb: float = 600.0
    
    # Performance monitoring
    enable_tuning_metrics: bool = True
    tuning_history_size: int = 1000
    impact_measurement_window_hours: int = 24


@dataclass
class QueryExpansionConfig:
    """Configuration for query expansion and understanding"""
    
    # Query understanding
    enable_entity_extraction: bool = ENABLE_QUERY_EXPANSION
    enable_relationship_extraction: bool = ENABLE_QUERY_EXPANSION
    enable_context_analysis: bool = ENABLE_QUERY_EXPANSION
    enable_implicit_requirements: bool = ENABLE_QUERY_EXPANSION
    
    # Expansion strategies
    enable_synonym_expansion: bool = ENABLE_QUERY_EXPANSION
    enable_related_terms_expansion: bool = ENABLE_QUERY_EXPANSION
    enable_contextual_expansion: bool = ENABLE_QUERY_EXPANSION
    enable_domain_expansion: bool = ENABLE_QUERY_EXPANSION
    enable_semantic_expansion: bool = ENABLE_QUERY_EXPANSION
    
    # Expansion limits
    max_expansions_per_query: int = 10
    max_entities_per_query: int = 5
    max_relationships_per_query: int = 3
    
    # Confidence thresholds
    entity_confidence_threshold: float = 0.6
    expansion_confidence_threshold: float = 0.5
    understanding_confidence_threshold: float = 0.7
    
    # Domain-specific settings
    enable_domain_synonyms: bool = True
    enable_temporal_context: bool = True
    enable_spatial_context: bool = True
    
    # Performance settings
    enable_caching: bool = ENABLE_QUERY_EXPANSION
    expansion_cache_ttl: int = 1800  # 30 minutes
    max_expansion_time_seconds: int = 5


@dataclass
class ResultDiversificationConfig:
    """Configuration for result diversification and ranking"""
    
    # Diversification strategies
    enable_maximal_marginal_relevance: bool = ENABLE_RESULT_DIVERSIFICATION
    enable_coverage_optimization: bool = ENABLE_RESULT_DIVERSIFICATION
    enable_novelty_ranking: bool = ENABLE_RESULT_DIVERSIFICATION
    enable_temporal_diversity: bool = ENABLE_RESULT_DIVERSIFICATION
    enable_domain_diversity: bool = ENABLE_RESULT_DIVERSIFICATION
    enable_perspective_diversity: bool = ENABLE_RESULT_DIVERSIFICATION
    
    # Ranking strategies
    default_ranking_strategy: str = 'balanced'  # 'balanced', 'relevance_focused', 'diversity_focused', 'coverage_focused'
    enable_adaptive_ranking: bool = True
    enable_reranking: bool = True
    
    # Diversity thresholds
    diversity_threshold: float = 0.3
    coverage_threshold: float = 0.5
    novelty_threshold: float = 0.4
    similarity_threshold: float = 0.7
    
    # Result limits
    max_results_per_strategy: int = 20
    min_results_per_strategy: int = 5
    max_total_results: int = 30
    
    # Performance settings
    enable_caching: bool = ENABLE_RESULT_DIVERSIFICATION
    diversification_cache_ttl: int = 1800  # 30 minutes
    max_diversification_time_seconds: int = 10
    
    # Quality metrics
    enable_diversity_metrics: bool = True
    enable_coverage_metrics: bool = True
    enable_relevance_distribution: bool = True


@dataclass
class EnhancedRetrievalConfig:
    """Enhanced retrieval system configuration"""
    
    # Global settings
    enable_enhanced_retrieval: bool = ENABLE_ENHANCED_RETRIEVAL
    enable_performance_monitoring: bool = ENABLE_PERFORMANCE_MONITORING
    enable_detailed_logging: bool = ENABLE_DETAILED_LOGGING
    
    # Embedding model
    embedding_model: str = "mxbai-embed-large:latest"
    
    # Cache settings
    enable_semantic_cache: bool = True
    semantic_cache_ttl: int = 3600  # 1 hour
    
    # Error handling
    enable_graceful_degradation: bool = True
    max_retry_attempts: int = 3
    
    # Existing configurations
    semantic_analyzer: SemanticAnalyzerConfig = None
    enhanced_scoring: EnhancedScoringConfig = None
    multi_stage_retriever: MultiStageRetrieverConfig = None
    hybrid_search: HybridSearchConfig = None
    cross_category: CrossCategoryConfig = None
    advanced_cache: AdvancedCacheConfig = None
    batch_processing: BatchProcessingConfig = None
    
    # Phase 5 configurations
    analytics: AnalyticsConfig = None
    performance_tuning: PerformanceTuningConfig = None
    
    # Phase 6 configurations
    query_expansion: QueryExpansionConfig = None
    result_diversification: ResultDiversificationConfig = None
    
    def __post_init__(self):
        """Initialize default configurations if not provided"""
        if self.semantic_analyzer is None:
            self.semantic_analyzer = SemanticAnalyzerConfig()
        if self.enhanced_scoring is None:
            self.enhanced_scoring = EnhancedScoringConfig()
        if self.multi_stage_retriever is None:
            self.multi_stage_retriever = MultiStageRetrieverConfig()
        if self.hybrid_search is None:
            self.hybrid_search = HybridSearchConfig()
        if self.cross_category is None:
            self.cross_category = CrossCategoryConfig()
        if self.advanced_cache is None:
            self.advanced_cache = AdvancedCacheConfig()
        if self.batch_processing is None:
            self.batch_processing = BatchProcessingConfig()
        if self.analytics is None:
            self.analytics = AnalyticsConfig()
        if self.performance_tuning is None:
            self.performance_tuning = PerformanceTuningConfig()
        if self.query_expansion is None:
            self.query_expansion = QueryExpansionConfig()
        if self.result_diversification is None:
            self.result_diversification = ResultDiversificationConfig()

def get_enhanced_retrieval_config() -> EnhancedRetrievalConfig:
    """Get enhanced retrieval configuration with environment variable overrides"""
    config = EnhancedRetrievalConfig()
    
    # Override with environment variables if present
    config.enable_enhanced_retrieval = os.getenv('ENABLE_ENHANCED_RETRIEVAL', 'true').lower() == 'true'
    config.enable_performance_monitoring = os.getenv('ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true'
    config.enable_detailed_logging = os.getenv('ENABLE_DETAILED_LOGGING', 'true').lower() == 'true'
    
    # Embedding model
    config.embedding_model = os.getenv('ENHANCED_EMBEDDING_MODEL', config.embedding_model)
    
    # Cache settings
    config.enable_semantic_cache = os.getenv('ENABLE_SEMANTIC_CACHE', 'true').lower() == 'true'
    config.semantic_cache_ttl = int(os.getenv('SEMANTIC_CACHE_TTL', str(config.semantic_cache_ttl)))
    
    # Error handling
    config.enable_graceful_degradation = os.getenv('ENABLE_GRACEFUL_DEGRADATION', 'true').lower() == 'true'
    config.max_retry_attempts = int(os.getenv('MAX_RETRY_ATTEMPTS', str(config.max_retry_attempts)))
    
    # Multi-stage retriever settings
    config.multi_stage_retriever.stage1_broad_k_multiplier = float(
        os.getenv('STAGE1_BROAD_K_MULTIPLIER', str(config.multi_stage_retriever.stage1_broad_k_multiplier))
    )
    config.multi_stage_retriever.stage1_min_k = int(
        os.getenv('STAGE1_MIN_K', str(config.multi_stage_retriever.stage1_min_k))
    )
    config.multi_stage_retriever.stage1_max_k = int(
        os.getenv('STAGE1_MAX_K', str(config.multi_stage_retriever.stage1_max_k))
    )
    config.multi_stage_retriever.stage2_rerank_top_n = int(
        os.getenv('STAGE2_RERANK_TOP_N', str(config.multi_stage_retriever.stage2_rerank_top_n))
    )
    config.multi_stage_retriever.stage3_final_top_n = int(
        os.getenv('STAGE3_FINAL_TOP_N', str(config.multi_stage_retriever.stage3_final_top_n))
    )
    config.multi_stage_retriever.semantic_threshold = float(
        os.getenv('SEMANTIC_THRESHOLD', str(config.multi_stage_retriever.semantic_threshold))
    )
    config.multi_stage_retriever.diversity_threshold = float(
        os.getenv('DIVERSITY_THRESHOLD', str(config.multi_stage_retriever.diversity_threshold))
    )
    
    # Enhanced scoring settings
    config.enhanced_scoring.semantic_similarity_threshold = float(
        os.getenv('SEMANTIC_SIMILARITY_THRESHOLD', str(config.enhanced_scoring.semantic_similarity_threshold))
    )
    config.enhanced_scoring.max_content_length_for_embedding = int(
        os.getenv('MAX_CONTENT_LENGTH_FOR_EMBEDDING', str(config.enhanced_scoring.max_content_length_for_embedding))
    )
    
    return config

def update_enhanced_retrieval_config(updates: Dict[str, Any]) -> EnhancedRetrievalConfig:
    """Update enhanced retrieval configuration with new values"""
    config = get_enhanced_retrieval_config()
    
    for key, value in updates.items():
        if hasattr(config, key):
            setattr(config, key, value)
        elif hasattr(config.semantic_analyzer, key):
            setattr(config.semantic_analyzer, key, value)
        elif hasattr(config.enhanced_scoring, key):
            setattr(config.enhanced_scoring, key, value)
        elif hasattr(config.multi_stage_retriever, key):
            setattr(config.multi_stage_retriever, key, value)
    
    return config

def is_enhanced_retrieval_enabled() -> bool:
    """Check if enhanced retrieval is enabled"""
    config = get_enhanced_retrieval_config()
    return config.enable_enhanced_retrieval

def get_embedding_model() -> str:
    """Get the configured embedding model"""
    config = get_enhanced_retrieval_config()
    return config.embedding_model

# Global configuration instance
_enhanced_retrieval_config = None

def get_config() -> EnhancedRetrievalConfig:
    """Get global enhanced retrieval configuration instance"""
    global _enhanced_retrieval_config
    if _enhanced_retrieval_config is None:
        _enhanced_retrieval_config = get_enhanced_retrieval_config()
    return _enhanced_retrieval_config 