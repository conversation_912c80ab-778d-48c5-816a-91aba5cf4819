#!/usr/bin/env python3
"""
Test script to debug the query service for "Bani" search
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

def test_query_service():
    """Test the actual query service"""
    print("=== Testing Query Service ===")
    try:
        from app.services.query_service import query_category
        
        # Test query for "Bani" in RISE category
        print("Testing query for 'Bani' in RISE category:")
        response = query_category(
            category='RISE',
            question='Bani',
            use_cross_category=True,
            session_id='test',
            device_fingerprint='test'
        )
        
        print(f"Response: {response}")
        print(f"Answer: {response.get('answer', 'No answer')}")
        print(f"Sources: {len(response.get('sources', []))}")
        for i, source in enumerate(response.get('sources', [])[:3]):
            print(f"  Source {i+1}: {source}")
            
    except Exception as e:
        print(f"Error in query service test: {e}")
        import traceback
        traceback.print_exc()

def test_query_service_without_cross_category():
    """Test query service without cross-category search"""
    print("\n=== Testing Query Service (No Cross-Category) ===")
    try:
        from app.services.query_service import query_category
        
        # Test query for "Bani" in RISE category without cross-category
        print("Testing query for 'Bani' in RISE category (no cross-category):")
        response = query_category(
            category='RISE',
            question='Bani',
            use_cross_category=False,
            session_id='test',
            device_fingerprint='test'
        )
        
        print(f"Response: {response}")
        print(f"Answer: {response.get('answer', 'No answer')}")
        print(f"Sources: {len(response.get('sources', []))}")
        for i, source in enumerate(response.get('sources', [])[:3]):
            print(f"  Source {i+1}: {source}")
            
    except Exception as e:
        print(f"Error in query service test: {e}")
        import traceback
        traceback.print_exc()

def test_hybrid_search_service():
    """Test the hybrid search service directly"""
    print("\n=== Testing Hybrid Search Service ===")
    try:
        from app.services.hybrid_search_service import hybrid_search
        
        # Test semantic search
        print("Testing hybrid search for 'Bani' in RISE category:")
        hybrid_result = hybrid_search(
            query='Bani',
            category='RISE',
            max_results=5
        )
        
        print(f"Hybrid search result: {hybrid_result}")
        if hasattr(hybrid_result, 'documents'):
            print(f"Found {len(hybrid_result.documents)} documents")
            for i, doc in enumerate(hybrid_result.documents[:3]):
                category = doc.metadata.get('category', 'Unknown')
                source = doc.metadata.get('source', 'Unknown')
                print(f"  {i+1}. Category: {category}, Source: {source}")
        
        # Test individual strategies
        print("\nTesting individual search strategies:")
        from app.services.hybrid_search_service import HybridSearchService, SearchStrategy
        
        hybrid_service = HybridSearchService()
        
        # Test semantic search
        print("Testing semantic search:")
        semantic_result = hybrid_service._semantic_search('Bani', 'RISE')
        print(f"Semantic search found {len(semantic_result.documents)} documents")
        
        # Test exact match search
        print("Testing exact match search:")
        exact_result = hybrid_service._exact_match_search('Bani', 'RISE')
        print(f"Exact match search found {len(exact_result.documents)} documents")
            
    except Exception as e:
        print(f"Error in hybrid search test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_query_service()
    test_query_service_without_cross_category()
    test_hybrid_search_service() 