{% extends "admin_base.html" %}

{% block title %}Manage Files{% endblock %}

{% block head %}
    {# Tailwind removed: migrated to Bootstrap 5 #}
{% endblock %}

{% block content %}
    <div class="card shadow-sm">
        <div class="card-body">
            <h1 class="h2 fw-bold text-dark mb-4">Manage Files</h1>

            <!-- Search Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('list_files') }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="search_query" class="form-label">Search Files</label>
                            <input type="text" class="form-control" id="search_query" name="search"
                                   value="{{ request.args.get('search', '') }}"
                                   placeholder="Search by filename, title, or author...">
                        </div>
                        <div class="col-md-3">
                            <label for="category_filter" class="form-label">Category</label>
                            <select class="form-select" id="category_filter" name="category">
                                <option value="">All Categories</option>
                                {% for category in files_data.keys() %}
                                <option value="{{ category }}" {% if request.args.get('category') == category %}selected{% endif %}>
                                    {{ category }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type_filter" class="form-label">Type</label>
                            <select class="form-select" id="type_filter" name="type">
                                <option value="">All Types</option>
                                <option value="pdf" {% if request.args.get('type') == 'pdf' %}selected{% endif %}>PDF</option>
                                <option value="url" {% if request.args.get('type') == 'url' %}selected{% endif %}>URL</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                    </form>
                    {% if request.args.get('search') or request.args.get('category') or request.args.get('type') %}
                    <div class="mt-3">
                        <a href="{{ url_for('list_files') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- List Files by Category -->
            {% if files_data %}
                <div class="mb-4">
                    <!-- Batch Delete Controls -->
                    <div class="mb-3 p-3 bg-white border rounded" id="batchControls" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span id="selectedCount" class="fw-medium text-primary">0 files selected</span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-danger" id="batchDeleteBtn" onclick="confirmBatchDelete()">
                                    <i class="fas fa-trash me-1"></i>Delete Selected
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="clearSelection()">
                                    Clear Selection
                                </button>
                            </div>
                        </div>
                    </div>

                    {% for category, files in files_data.items() %}
                        <div class="bg-light p-4 rounded mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h2 class="h4 fw-semibold text-dark mb-0">{{ category }}</h2>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll_{{ category }}"
                                           onchange="toggleCategorySelection('{{ category }}', this.checked)">
                                    <label class="form-check-label" for="selectAll_{{ category }}">
                                        Select All in {{ category }}
                                    </label>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="text-muted fw-medium" style="width: 40px;">
                                                <input class="form-check-input" type="checkbox" id="selectAllTable_{{ category }}"
                                                       onchange="toggleTableSelection('{{ category }}', this.checked)">
                                            </th>
                                            <th class="text-muted fw-medium">File/URL Name</th>
                                            <th class="text-muted fw-medium">Type</th>
                                            <th class="text-muted fw-medium">Metadata</th>
                                            <th class="text-muted fw-medium">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for file in files %}
                                            <tr data-category="{{ category }}" data-filename="{{ file.source }}" data-filetype="{{ file.type }}">
                                                <td>
                                                    <input class="form-check-input file-checkbox" type="checkbox"
                                                           data-category="{{ category }}" data-filename="{{ file.source }}" data-filetype="{{ file.type }}"
                                                           onchange="updateBatchControls()">
                                                </td>
                                                <td class="text-dark">
                                                    {% if file.type == 'url' %}
                                                        <a href="{{ file.original_filename }}" target="_blank" class="text-primary text-decoration-underline text-truncate d-block" style="max-width: 300px;" title="{{ file.original_filename }}">
                                                            {{ file.original_filename }}
                                                        </a>
                                                        {% if file.scrape_depth is defined and file.scrape_depth > 0 %}
                                                            <span class="small text-muted mt-1 d-block">
                                                                Depth: {{ file.scrape_depth }}
                                                                {% if file.pages_scraped is defined %}
                                                                    ({{ file.pages_scraped }} pages)
                                                                {% endif %}
                                                            </span>
                                                        {% endif %}

                                                        {% if file.database_retrieval is defined and file.database_retrieval %}
                                                            <span class="small text-success mt-1 d-block">
                                                                <svg class="d-inline-block me-1" width="12" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                </svg>
                                                                Using database content
                                                                {% if file.url_last_scraped is defined %}
                                                                    (Last scraped: {{ file.url_last_scraped.split('T')[0] }})
                                                                {% endif %}
                                                            </span>
                                                        {% endif %}

                                                        {% if file.source_url_id is defined %}
                                                            <span class="small text-muted mt-1 d-block">
                                                                Database ID: {{ file.source_url_id }}
                                                            </span>
                                                        {% endif %}
                                                    {% elif file.original_url %}
                                                                                                <div>
                                            <span title="{{ file.source }}">{{ file.source }}</span>
                                            <div class="mt-1">
                                                                <a href="{{ file.original_url }}" target="_blank" class="small text-primary text-decoration-underline text-truncate d-block" style="max-width: 300px;" title="{{ file.original_url }}">
                                                                    Source: {{ file.original_url }}
                                                                </a>
                                                            </div>

                                                            {% if file.database_retrieval is defined and file.database_retrieval %}
                                                                <span class="small text-success mt-1 d-block">
                                                                    <svg class="d-inline-block me-1" width="12" height="12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                                    </svg>
                                                                    Using database content
                                                                </span>
                                                            {% endif %}

                                                            {% if file.source_url_id is defined or file.pdf_document_id is defined or file.cover_image_id is defined %}
                                                                <div class="small text-muted mt-1">
                                                                    {% if file.source_url_id is defined %}
                                                                        <span class="d-block">URL ID: {{ file.source_url_id }}</span>
                                                                    {% endif %}
                                                                    {% if file.pdf_document_id is defined %}
                                                                        <span class="d-block">PDF ID: {{ file.pdf_document_id }}</span>
                                                                    {% endif %}
                                                                    {% if file.cover_image_id is defined %}
                                                                        <span class="d-block">Cover Image ID: {{ file.cover_image_id }}</span>
                                                                    {% endif %}
                                                                </div>
                                                            {% endif %}
                                                        </div>
                                                                                        {% else %}
                                        <span title="{{ file.source }}">{{ file.source }}</span>
                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge {% if file.type == 'pdf' %}bg-primary{% else %}bg-success{% endif %}">
                                                        {{ file.type | upper }}
                                                    </span>

                                                    {% if file.database_retrieval is defined and file.database_retrieval %}
                                                        <span class="badge bg-success ms-1">
                                                            DB
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if file.type == 'pdf' and file.pdf_document_id %}
                                                        <div class="small">
                                                            {% if file.pdf_title %}
                                                                <div class="text-truncate" style="max-width: 200px;" title="{{ file.pdf_title }}">
                                                                    <strong>{{ file.pdf_title }}</strong>
                                                                </div>
                                                            {% endif %}
                                                            {% if file.pdf_author %}
                                                                <div class="text-muted text-truncate" style="max-width: 200px;" title="{{ file.pdf_author }}">
                                                                    {{ file.pdf_author }}
                                                                </div>
                                                            {% endif %}
                                                            {% if file.page_count %}
                                                                <span class="badge bg-light text-dark">{{ file.page_count }} pages</span>
                                                            {% endif %}
                                                            {% if file.file_size %}
                                                                <span class="badge bg-light text-dark">
                                                                    {% if file.file_size > 1048576 %}
                                                                        {{ (file.file_size / 1024 / 1024) | round(1) }} MB
                                                                    {% else %}
                                                                        {{ (file.file_size / 1024) | round(1) }} KB
                                                                    {% endif %}
                                                                </span>
                                                            {% endif %}
                                                        </div>
                                                    {% elif file.type == 'url' %}
                                                        <div class="small text-muted">
                                                            {% if file.url_last_scraped %}
                                                                Last scraped: {{ file.url_last_scraped.split('T')[0] }}
                                                            {% endif %}
                                                        </div>
                                                    {% else %}
                                                        <span class="text-muted small">No metadata</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        {% if file.type == 'pdf' and file.pdf_document_id %}
                                                            <a href="{{ url_for('view_pdf_details', pdf_id=file.pdf_document_id) }}"
                                                               class="btn btn-info btn-sm" title="View PDF Details">
                                                                <i class="fas fa-info-circle"></i>
                                                            </a>
                                                        {% endif %}
                                                        <a href="{{ url_for('view_vector_data', category=category, filename=file.source) }}"
                                                           class="btn btn-primary btn-sm" title="View Vector Data">
                                                            <i class="fas fa-search"></i>
                                                        </a>
                                                        <form method="POST" action="{{ url_for('delete_file_route', category=category, filename=file.source) }}"
                                                              onsubmit="return handleDeleteSubmit(this, '{{ file.type }}');" class="d-inline">
                                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-warning d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <svg class="me-2" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div>
                        <p class="mb-0">
                            No files or URLs found. <a href="{{ url_for('upload_file') }}" class="fw-medium text-decoration-underline">Upload some content</a> to get started.
                        </p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
function handleDeleteSubmit(form, fileType) {
    // Prevent double submission
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton.disabled) {
        return false;
    }

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to delete this ${fileType}? This will also delete all associated vector data.`)) {
        return false;
    }

    // Disable the submit button to prevent double submission
    submitButton.disabled = true;
    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>Deleting...';

    // Allow the form to submit normally - the server will redirect after deletion
    // Don't re-enable the button since we're redirecting anyway

    return true;
}

// Batch delete functionality
function updateBatchControls() {
    const checkboxes = document.querySelectorAll('.file-checkbox:checked');
    const batchControls = document.getElementById('batchControls');
    const selectedCount = document.getElementById('selectedCount');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');

    if (checkboxes.length > 0) {
        batchControls.style.display = 'block';
        selectedCount.textContent = `${checkboxes.length} file${checkboxes.length > 1 ? 's' : ''} selected`;
        batchDeleteBtn.disabled = false;
    } else {
        batchControls.style.display = 'none';
        batchDeleteBtn.disabled = true;
    }

    // Update category select-all checkboxes
    updateCategoryCheckboxes();
}

function updateCategoryCheckboxes() {
    const categories = new Set();
    document.querySelectorAll('.file-checkbox').forEach(checkbox => {
        categories.add(checkbox.dataset.category);
    });

    categories.forEach(category => {
        const categoryCheckboxes = document.querySelectorAll(`.file-checkbox[data-category="${category}"]`);
        const checkedCategoryCheckboxes = document.querySelectorAll(`.file-checkbox[data-category="${category}"]:checked`);
        const selectAllCategory = document.getElementById(`selectAll_${category}`);
        const selectAllTable = document.getElementById(`selectAllTable_${category}`);

        if (checkedCategoryCheckboxes.length === 0) {
            if (selectAllCategory) selectAllCategory.indeterminate = false, selectAllCategory.checked = false;
            if (selectAllTable) selectAllTable.indeterminate = false, selectAllTable.checked = false;
        } else if (checkedCategoryCheckboxes.length === categoryCheckboxes.length) {
            if (selectAllCategory) selectAllCategory.indeterminate = false, selectAllCategory.checked = true;
            if (selectAllTable) selectAllTable.indeterminate = false, selectAllTable.checked = true;
        } else {
            if (selectAllCategory) selectAllCategory.indeterminate = true, selectAllCategory.checked = false;
            if (selectAllTable) selectAllTable.indeterminate = true, selectAllTable.checked = false;
        }
    });
}

function toggleCategorySelection(category, checked) {
    const checkboxes = document.querySelectorAll(`.file-checkbox[data-category="${category}"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
    });

    // Update table header checkbox
    const selectAllTable = document.getElementById(`selectAllTable_${category}`);
    if (selectAllTable) {
        selectAllTable.checked = checked;
        selectAllTable.indeterminate = false;
    }

    updateBatchControls();
}

function toggleTableSelection(category, checked) {
    const checkboxes = document.querySelectorAll(`.file-checkbox[data-category="${category}"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
    });

    // Update category header checkbox
    const selectAllCategory = document.getElementById(`selectAll_${category}`);
    if (selectAllCategory) {
        selectAllCategory.checked = checked;
        selectAllCategory.indeterminate = false;
    }

    updateBatchControls();
}

function clearSelection() {
    document.querySelectorAll('.file-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.querySelectorAll('[id^="selectAll_"]').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.indeterminate = false;
    });
    updateBatchControls();
}

function confirmBatchDelete() {
    const checkboxes = document.querySelectorAll('.file-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('No files selected for deletion.');
        return;
    }

    const filesByCategory = {};
    checkboxes.forEach(checkbox => {
        const category = checkbox.dataset.category;
        const filename = checkbox.dataset.filename;
        const filetype = checkbox.dataset.filetype;

        if (!filesByCategory[category]) {
            filesByCategory[category] = [];
        }
        filesByCategory[category].push({ filename, filetype });
    });

    // Build confirmation message
    let message = `Are you sure you want to delete ${checkboxes.length} file${checkboxes.length > 1 ? 's' : ''}?\n\n`;
    Object.keys(filesByCategory).forEach(category => {
        message += `${category}: ${filesByCategory[category].length} file${filesByCategory[category].length > 1 ? 's' : ''}\n`;
    });
    message += '\nThis will also delete all associated vector data and cannot be undone.';

    if (confirm(message)) {
        performBatchDelete(filesByCategory);
    }
}

async function performBatchDelete(filesByCategory) {
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const originalText = batchDeleteBtn.innerHTML;

    // Disable button and show loading state
    batchDeleteBtn.disabled = true;
    batchDeleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>Deleting...';

    try {
        // Debug logging
        console.log('=== BATCH DELETE REQUEST START ===');
        console.log('Files by category:', filesByCategory);
        const totalFiles = Object.values(filesByCategory).flat().length;
        console.log('Total files:', totalFiles);
        
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('input[name="csrf_token"]')?.value;

        console.log('CSRF token found:', !!csrfToken);
        if (!csrfToken) {
            throw new Error('CSRF token not found');
        }

        // Check if we need to chunk the request
        const MAX_FILES_PER_BATCH = 100;
        if (totalFiles > MAX_FILES_PER_BATCH) {
            console.log(`Large batch detected (${totalFiles} files). Processing in chunks of ${MAX_FILES_PER_BATCH}...`);
            
            // Process in chunks
            const results = await processBatchDeleteInChunks(filesByCategory, csrfToken, MAX_FILES_PER_BATCH);
            
            // Show combined results
            showBatchDeleteResults(results);
            
            // Reload the page to refresh the file list
            setTimeout(() => {
                window.location.reload();
            }, 2000);
            
            return;
        }

        // Process single batch (original logic)
        const requestBody = { files: filesByCategory };
        console.log('Request body:', requestBody);
        console.log('Request body JSON:', JSON.stringify(requestBody));

        const response = await fetch('/api/files/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        const result = await response.json();
        console.log('Response result:', result);

        if ((response.ok || response.status === 207) && result.success !== false) {
            // Show detailed results
            showBatchDeleteResults(result);

            // Reload the page to refresh the file list
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            throw new Error(result.message || result.error || 'Failed to delete files');
        }
    } catch (error) {
        console.error('Batch delete error:', error);
        let errorMsg = 'Error during batch deletion. See console for details.';
        if (error.response) {
            const result = await error.response.json();
            errorMsg = result.message || result.error || 'Failed to delete files';
        }
        alert(errorMsg);

        // Re-enable button
        batchDeleteBtn.disabled = false;
        batchDeleteBtn.innerHTML = originalText;
    }
}

async function processBatchDeleteInChunks(filesByCategory, csrfToken, maxFilesPerBatch) {
    console.log('=== PROCESSING BATCH DELETE IN CHUNKS ===');
    
    // Flatten all files into a single array with category info
    const allFiles = [];
    for (const [category, files] of Object.entries(filesByCategory)) {
        for (const file of files) {
            allFiles.push({ ...file, category });
        }
    }
    
    console.log(`Total files to process: ${allFiles.length}`);
    
    // Process in chunks
    const chunks = [];
    for (let i = 0; i < allFiles.length; i += maxFilesPerBatch) {
        chunks.push(allFiles.slice(i, i + maxFilesPerBatch));
    }
    
    console.log(`Processing ${chunks.length} chunks...`);
    
    // Aggregate results
    const combinedResults = {
        success: true,
        successful_count: 0,
        failed_count: 0,
        total_count: allFiles.length,
        results: {
            successful: [],
            failed: []
        },
        errors: [],
        chunks_processed: 0,
        total_chunks: chunks.length
    };
    
    // Process each chunk
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        const chunk = chunks[chunkIndex];
        console.log(`Processing chunk ${chunkIndex + 1}/${chunks.length} with ${chunk.length} files`);
        
        // Update button text to show progress
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        batchDeleteBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>Deleting... (${chunkIndex + 1}/${chunks.length})`;
        
        // Group files by category for this chunk
        const chunkByCategory = {};
        for (const file of chunk) {
            if (!chunkByCategory[file.category]) {
                chunkByCategory[file.category] = [];
            }
            chunkByCategory[file.category].push({
                filename: file.filename,
                filetype: file.filetype
            });
        }
        
        try {
            const response = await fetch('/api/files/batch-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ files: chunkByCategory })
            });
            
            const result = await response.json();
            console.log(`Chunk ${chunkIndex + 1} result:`, result);
            
            // Aggregate results
            if (result.successful_count) {
                combinedResults.successful_count += result.successful_count;
                combinedResults.results.successful.push(...(result.results?.successful || []));
            }
            
            if (result.failed_count) {
                combinedResults.failed_count += result.failed_count;
                combinedResults.results.failed.push(...(result.results?.failed || []));
            }
            
            if (result.errors) {
                combinedResults.errors.push(...result.errors);
            }
            
            combinedResults.chunks_processed++;
            
            // Add small delay between chunks to prevent overwhelming the server
            if (chunkIndex < chunks.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
        } catch (error) {
            console.error(`Error processing chunk ${chunkIndex + 1}:`, error);
            combinedResults.failed_count += chunk.length;
            combinedResults.errors.push(`Chunk ${chunkIndex + 1} failed: ${error.message}`);
            combinedResults.chunks_processed++;
        }
    }
    
    // Determine overall success
    combinedResults.success = combinedResults.successful_count > 0;
    
    console.log('=== CHUNK PROCESSING COMPLETE ===');
    console.log('Combined results:', combinedResults);
    
    return combinedResults;
}

function showBatchDeleteResults(result) {
    // Create a modal-like dialog for better user experience
    const modalHtml = `
        <div class="modal fade" id="batchDeleteResultsModal" tabindex="-1" aria-labelledby="batchDeleteResultsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="batchDeleteResultsModalLabel">
                            <i class="fas fa-trash me-2"></i>Batch Delete Results
                            ${result.total_chunks ? `<small class="text-muted ms-2">(Processed in ${result.total_chunks} chunks)</small>` : ''}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="card text-center ${result.successful_count > 0 ? 'border-success' : ''}">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">${result.successful_count}</h5>
                                        <p class="card-text">Successfully Deleted</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center ${result.failed_count > 0 ? 'border-danger' : ''}">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger">${result.failed_count}</h5>
                                        <p class="card-text">Failed to Delete</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-info">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">${result.total_count || result.successful_count + result.failed_count}</h5>
                                        <p class="card-text">Total Files</p>
                                    </div>
                                </div>
                            </div>
                            ${result.total_chunks ? `
                            <div class="col-md-3">
                                <div class="card text-center border-warning">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">${result.chunks_processed}/${result.total_chunks}</h5>
                                        <p class="card-text">Chunks Processed</p>
                                    </div>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        ${result.successful_count > 0 ? `
                        <div class="mb-3">
                            <h6 class="text-success"><i class="fas fa-check-circle me-1"></i>Successfully Deleted Files:</h6>
                            <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                ${result.results?.successful?.map(file => `
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><strong>${file.category}</strong>/${file.filename}</span>
                                        <span class="badge bg-success rounded-pill">${file.filetype}</span>
                                    </div>
                                `).join('') || ''}
                            </div>
                        </div>
                        ` : ''}

                        ${result.failed_count > 0 ? `
                        <div class="mb-3">
                            <h6 class="text-danger"><i class="fas fa-exclamation-circle me-1"></i>Failed to Delete:</h6>
                            <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                ${result.results?.failed?.map(file => `
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span><strong>${file.category}</strong>/${file.filename}</span>
                                            <span class="badge bg-danger rounded-pill">${file.filetype}</span>
                                        </div>
                                        <small class="text-muted">${file.error}</small>
                                    </div>
                                `).join('') || ''}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove any existing modal
    const existingModal = document.getElementById('batchDeleteResultsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('batchDeleteResultsModal'));
    modal.show();

    // Clean up modal after it's hidden
    document.getElementById('batchDeleteResultsModal').addEventListener('hidden.bs.modal', function () {
        this.remove();
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add change listeners to all file checkboxes
    document.querySelectorAll('.file-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBatchControls);
    });

    // Initial update
    updateBatchControls();
});
</script>
{% endblock %}