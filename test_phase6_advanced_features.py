#!/usr/bin/env python3
"""
Phase 6: Advanced Features Test Suite

This script tests the advanced query understanding, expansion, and result diversification
features implemented in Phase 6 of the enhanced retrieval system.
"""

import sys
import os
import time
import logging
from datetime import datetime
from typing import List, Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all Phase 6 modules can be imported successfully"""
    logger.info("Testing Phase 6 module imports...")
    
    try:
        # Test query expansion imports
        from app.services.query_expander import (
            QueryExpander, QueryExpansion, QueryUnderstanding, QueryVariation,
            understand_query, expand_query, generate_query_variations
        )
        logger.info("✓ Query expansion modules imported successfully")
        
        # Test result diversification imports
        from app.services.result_diversifier import (
            ResultDiversifier, DiversifiedResult, DiversificationConfig, RankingStrategy,
            diversify_results
        )
        logger.info("✓ Result diversification modules imported successfully")
        
        # Test configuration imports
        from config.enhanced_retrieval_config import (
            QueryExpansionConfig, ResultDiversificationConfig, EnhancedRetrievalConfig
        )
        logger.info("✓ Phase 6 configuration modules imported successfully")
        
        # Test performance monitoring imports
        from app.utils.rag_performance import (
            track_query_expansion_metrics, track_result_diversification_metrics
        )
        logger.info("✓ Phase 6 performance monitoring imported successfully")
        
        # Test supporting modules
        from app.services.semantic_analyzer import SemanticAnalyzer, QueryAnalysis
        from app.services.enhanced_relevance_scorer import EnhancedRelevanceScorer
        from langchain.schema import Document
        logger.info("✓ Supporting modules imported successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        return False

def test_query_expansion_configuration():
    """Test query expansion configuration"""
    logger.info("Testing query expansion configuration...")
    
    try:
        from config.enhanced_retrieval_config import QueryExpansionConfig, EnhancedRetrievalConfig
        
        # Test configuration creation
        config = QueryExpansionConfig()
        assert config.enable_entity_extraction == True
        assert config.enable_relationship_extraction == True
        assert config.max_expansions_per_query == 10
        assert config.entity_confidence_threshold == 0.6
        logger.info("✓ Query expansion configuration created successfully")
        
        # Test configuration integration
        enhanced_config = EnhancedRetrievalConfig()
        assert enhanced_config.query_expansion is not None
        assert isinstance(enhanced_config.query_expansion, QueryExpansionConfig)
        logger.info("✓ Query expansion configuration integrated successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration error: {e}")
        return False

def test_result_diversification_configuration():
    """Test result diversification configuration"""
    logger.info("Testing result diversification configuration...")
    
    try:
        from config.enhanced_retrieval_config import ResultDiversificationConfig, EnhancedRetrievalConfig
        
        # Test configuration creation
        config = ResultDiversificationConfig()
        assert config.enable_maximal_marginal_relevance == True
        assert config.enable_coverage_optimization == True
        assert config.default_ranking_strategy == 'balanced'
        assert config.diversity_threshold == 0.3
        logger.info("✓ Result diversification configuration created successfully")
        
        # Test configuration integration
        enhanced_config = EnhancedRetrievalConfig()
        assert enhanced_config.result_diversification is not None
        assert isinstance(enhanced_config.result_diversification, ResultDiversificationConfig)
        logger.info("✓ Result diversification configuration integrated successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration error: {e}")
        return False

def test_query_understanding():
    """Test query understanding functionality"""
    logger.info("Testing query understanding...")
    
    try:
        from app.services.query_expander import understand_query
        
        # Test basic query understanding
        query = "What are the environmental impacts of deforestation in Southeast Asia?"
        understanding = understand_query(query)
        
        assert isinstance(understanding.entities, list)
        assert isinstance(understanding.relationships, list)
        assert isinstance(understanding.context_clues, list)
        assert isinstance(understanding.domain_specific_terms, list)
        assert isinstance(understanding.confidence, float)
        assert 0.0 <= understanding.confidence <= 1.0
        
        logger.info(f"✓ Query understanding completed")
        logger.info(f"  Entities: {understanding.entities}")
        logger.info(f"  Domain terms: {understanding.domain_specific_terms}")
        logger.info(f"  Confidence: {understanding.confidence:.3f}")
        
        # Test query with temporal context
        temporal_query = "What were the forest management practices in 2020?"
        temporal_understanding = understand_query(temporal_query)
        
        if temporal_understanding.temporal_context:
            logger.info(f"✓ Temporal context detected: {temporal_understanding.temporal_context}")
        
        # Test query with spatial context
        spatial_query = "What are the forestry regulations in California?"
        spatial_understanding = understand_query(spatial_query)
        
        if spatial_understanding.spatial_context:
            logger.info(f"✓ Spatial context detected: {spatial_understanding.spatial_context}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Query understanding error: {e}")
        return False

def test_query_expansion():
    """Test query expansion functionality"""
    logger.info("Testing query expansion...")
    
    try:
        from app.services.query_expander import expand_query
        
        # Test comprehensive expansion
        query = "forest management"
        expansion = expand_query(query, strategy="comprehensive")
        
        assert isinstance(expansion.expanded_queries, list)
        assert len(expansion.expanded_queries) > 0
        assert expansion.expansion_type == "comprehensive"
        assert isinstance(expansion.confidence, float)
        assert 0.0 <= expansion.confidence <= 1.0
        
        logger.info(f"✓ Comprehensive expansion completed")
        logger.info(f"  Original query: {expansion.original_query}")
        logger.info(f"  Expanded queries: {expansion.expanded_queries[:3]}...")
        logger.info(f"  Confidence: {expansion.confidence:.3f}")
        
        # Test focused expansion
        focused_expansion = expand_query(query, strategy="focused")
        assert focused_expansion.expansion_type == "focused"
        logger.info(f"✓ Focused expansion completed")
        
        # Test semantic expansion
        semantic_expansion = expand_query(query, strategy="semantic")
        assert semantic_expansion.expansion_type == "semantic"
        logger.info(f"✓ Semantic expansion completed")
        
        # Test domain expansion
        domain_expansion = expand_query(query, strategy="domain")
        assert domain_expansion.expansion_type == "domain"
        logger.info(f"✓ Domain expansion completed")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Query expansion error: {e}")
        return False

def test_query_variations():
    """Test query variation generation"""
    logger.info("Testing query variation generation...")
    
    try:
        from app.services.query_expander import generate_query_variations
        
        # Test variation generation
        query = "sustainable forestry practices"
        variations = generate_query_variations(query)
        
        assert isinstance(variations, list)
        assert len(variations) > 0
        
        expected_strategies = {'semantic', 'keyword', 'hybrid', 'exact'}
        actual_strategies = {var.strategy for var in variations}
        
        logger.info(f"✓ Query variations generated")
        logger.info(f"  Original query: {query}")
        logger.info(f"  Strategies: {actual_strategies}")
        
        for variation in variations:
            assert isinstance(variation.query, str)
            assert isinstance(variation.strategy, str)
            assert isinstance(variation.weight, float)
            assert 0.0 <= variation.weight <= 1.0
            assert isinstance(variation.expected_documents, int)
            assert variation.expected_documents > 0
            
            logger.info(f"    {variation.strategy}: {variation.query} (weight: {variation.weight:.2f})")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Query variation error: {e}")
        return False

def test_result_diversification():
    """Test result diversification functionality"""
    logger.info("Testing result diversification...")
    
    try:
        from app.services.result_diversifier import diversify_results
        from langchain.schema import Document
        
        # Create sample documents for testing
        sample_docs = [
            Document(
                page_content="Sustainable forest management practices include selective logging and reforestation.",
                metadata={"source": "doc1", "category": "forestry"}
            ),
            Document(
                page_content="Environmental impacts of deforestation include habitat loss and climate change.",
                metadata={"source": "doc2", "category": "environmental"}
            ),
            Document(
                page_content="Forest management regulations require proper permits and environmental assessments.",
                metadata={"source": "doc3", "category": "regulatory"}
            ),
            Document(
                page_content="Timber harvesting techniques vary by region and forest type.",
                metadata={"source": "doc4", "category": "forestry"}
            ),
            Document(
                page_content="Biodiversity conservation in managed forests requires careful planning.",
                metadata={"source": "doc5", "category": "conservation"}
            )
        ]
        
        # Test balanced diversification
        query = "sustainable forest management"
        result = diversify_results(sample_docs, query, strategy="balanced", max_results=3)
        
        assert isinstance(result.documents, list)
        assert len(result.documents) <= 3
        assert isinstance(result.diversity_score, float)
        assert isinstance(result.coverage_score, float)
        assert result.ranking_strategy == "balanced"
        
        logger.info(f"✓ Balanced diversification completed")
        logger.info(f"  Documents: {len(result.documents)}")
        logger.info(f"  Diversity score: {result.diversity_score:.3f}")
        logger.info(f"  Coverage score: {result.coverage_score:.3f}")
        
        # Test relevance-focused diversification
        relevance_result = diversify_results(sample_docs, query, strategy="relevance_focused", max_results=3)
        assert relevance_result.ranking_strategy == "relevance_focused"
        logger.info(f"✓ Relevance-focused diversification completed")
        
        # Test diversity-focused diversification
        diversity_result = diversify_results(sample_docs, query, strategy="diversity_focused", max_results=3)
        assert diversity_result.ranking_strategy == "diversity_focused"
        logger.info(f"✓ Diversity-focused diversification completed")
        
        # Test coverage-focused diversification
        coverage_result = diversify_results(sample_docs, query, strategy="coverage_focused", max_results=3)
        assert coverage_result.ranking_strategy == "coverage_focused"
        logger.info(f"✓ Coverage-focused diversification completed")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Result diversification error: {e}")
        return False

def test_diversity_metrics():
    """Test diversity metrics calculation"""
    logger.info("Testing diversity metrics calculation...")
    
    try:
        from app.services.result_diversifier import ResultDiversifier
        from app.services.enhanced_relevance_scorer import ScoredDocument
        from langchain.schema import Document
        
        diversifier = ResultDiversifier()
        
        # Create test documents
        raw_docs = [
            Document(page_content="Forest management practices and sustainability."),
            Document(page_content="Environmental conservation and biodiversity protection."),
            Document(page_content="Timber harvesting and logging regulations."),
            Document(page_content="Climate change impacts on forest ecosystems."),
            Document(page_content="Community forestry and local participation.")
        ]
        
        # Convert to ScoredDocument objects
        from app.services.enhanced_relevance_scorer import RelevanceScore
        
        docs = []
        for doc in raw_docs:
            relevance_score = RelevanceScore(
                overall_score=0.8,
                semantic_score=0.7,
                keyword_score=0.6,
                contextual_score=0.5,
                temporal_score=0.4,
                domain_score=0.3,
                confidence=0.75,
                scoring_metadata={}
            )
            scored_doc = ScoredDocument(
                document=doc,
                relevance_score=relevance_score,
                ranking_position=0,
                score_breakdown={}
            )
            docs.append(scored_doc)
        
        # Test diversity metrics
        diversity_metrics = diversifier._calculate_diversity_metrics(docs)
        
        assert isinstance(diversity_metrics, dict)
        assert 'average_similarity' in diversity_metrics
        assert 'diversity_score' in diversity_metrics
        assert 'similarity_std' in diversity_metrics
        assert 'unique_documents_ratio' in diversity_metrics
        
        logger.info(f"✓ Diversity metrics calculated")
        logger.info(f"  Average similarity: {diversity_metrics['average_similarity']:.3f}")
        logger.info(f"  Diversity score: {diversity_metrics['diversity_score']:.3f}")
        logger.info(f"  Similarity std: {diversity_metrics['similarity_std']:.3f}")
        logger.info(f"  Unique ratio: {diversity_metrics['unique_documents_ratio']:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Diversity metrics error: {e}")
        return False

def test_performance_monitoring():
    """Test Phase 6 performance monitoring integration"""
    logger.info("Testing Phase 6 performance monitoring...")
    
    try:
        from app.utils.rag_performance import (
            track_query_expansion_metrics, track_result_diversification_metrics,
            get_rag_monitor
        )
        
        # Test query expansion metrics
        track_query_expansion_metrics(
            operation_type="test_expansion",
            execution_time=0.5,
            documents_processed=1,
            expansions_generated=5,
            confidence_score=0.8
        )
        logger.info("✓ Query expansion metrics tracked")
        
        # Test result diversification metrics
        track_result_diversification_metrics(
            operation_type="test_diversification",
            execution_time=0.3,
            documents_processed=5,
            diversity_score=0.7,
            coverage_score=0.8,
            ranking_strategy="balanced"
        )
        logger.info("✓ Result diversification metrics tracked")
        
        # Test monitor access
        monitor = get_rag_monitor()
        assert monitor is not None
        logger.info("✓ Performance monitor accessed successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Performance monitoring error: {e}")
        return False

def test_integration_with_existing_system():
    """Test integration with existing system components"""
    logger.info("Testing integration with existing system...")
    
    try:
        # Test integration with semantic analyzer
        from app.services.semantic_analyzer import SemanticAnalyzer
        from app.services.query_expander import QueryExpander
        
        semantic_analyzer = SemanticAnalyzer()
        query_expander = QueryExpander()
        
        # Test that query expander uses semantic analyzer
        query = "What are the best practices for forest management?"
        analysis = semantic_analyzer.analyze_query(query)
        understanding = query_expander.understand_query(query)
        
        assert analysis is not None
        assert understanding is not None
        logger.info("✓ Integration with semantic analyzer verified")
        
        # Test integration with enhanced relevance scorer
        from app.services.enhanced_relevance_scorer import EnhancedRelevanceScorer
        from app.services.result_diversifier import ResultDiversifier
        
        relevance_scorer = EnhancedRelevanceScorer()
        result_diversifier = ResultDiversifier()
        
        # Test that result diversifier uses relevance scorer
        assert result_diversifier.relevance_scorer is not None
        assert result_diversifier.semantic_analyzer is not None
        logger.info("✓ Integration with enhanced relevance scorer verified")
        
        # Test configuration integration
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        assert config.query_expansion is not None
        assert config.result_diversification is not None
        logger.info("✓ Configuration integration verified")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Integration error: {e}")
        return False

def test_error_handling():
    """Test error handling in Phase 6 components"""
    logger.info("Testing error handling...")
    
    try:
        from app.services.query_expander import understand_query, expand_query
        from app.services.result_diversifier import diversify_results
        from langchain.schema import Document
        
        # Test empty query handling
        try:
            understanding = understand_query("")
            logger.info("✓ Empty query handled gracefully")
        except Exception as e:
            logger.info(f"✓ Empty query error caught: {type(e).__name__}")
        
        # Test invalid expansion strategy
        try:
            expansion = expand_query("test", strategy="invalid_strategy")
        except ValueError as e:
            logger.info("✓ Invalid expansion strategy error caught")
        
        # Test empty document list
        try:
            result = diversify_results([], "test query")
            assert len(result.documents) == 0
            logger.info("✓ Empty document list handled gracefully")
        except Exception as e:
            logger.info(f"✓ Empty document list error caught: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Error handling test failed: {e}")
        return False

def main():
    """Run all Phase 6 tests"""
    logger.info("=" * 60)
    logger.info("Phase 6: Advanced Features Test Suite")
    logger.info("=" * 60)
    
    start_time = time.time()
    tests_passed = 0
    total_tests = 10
    
    # Test 1: Module imports
    if test_imports():
        tests_passed += 1
        logger.info("✓ Test 1/10: Module imports - PASSED")
    else:
        logger.error("✗ Test 1/10: Module imports - FAILED")
    
    # Test 2: Query expansion configuration
    if test_query_expansion_configuration():
        tests_passed += 1
        logger.info("✓ Test 2/10: Query expansion configuration - PASSED")
    else:
        logger.error("✗ Test 2/10: Query expansion configuration - FAILED")
    
    # Test 3: Result diversification configuration
    if test_result_diversification_configuration():
        tests_passed += 1
        logger.info("✓ Test 3/10: Result diversification configuration - PASSED")
    else:
        logger.error("✗ Test 3/10: Result diversification configuration - FAILED")
    
    # Test 4: Query understanding
    if test_query_understanding():
        tests_passed += 1
        logger.info("✓ Test 4/10: Query understanding - PASSED")
    else:
        logger.error("✗ Test 4/10: Query understanding - FAILED")
    
    # Test 5: Query expansion
    if test_query_expansion():
        tests_passed += 1
        logger.info("✓ Test 5/10: Query expansion - PASSED")
    else:
        logger.error("✗ Test 5/10: Query expansion - FAILED")
    
    # Test 6: Query variations
    if test_query_variations():
        tests_passed += 1
        logger.info("✓ Test 6/10: Query variations - PASSED")
    else:
        logger.error("✗ Test 6/10: Query variations - FAILED")
    
    # Test 7: Result diversification
    if test_result_diversification():
        tests_passed += 1
        logger.info("✓ Test 7/10: Result diversification - PASSED")
    else:
        logger.error("✗ Test 7/10: Result diversification - FAILED")
    
    # Test 8: Diversity metrics
    if test_diversity_metrics():
        tests_passed += 1
        logger.info("✓ Test 8/10: Diversity metrics - PASSED")
    else:
        logger.error("✗ Test 8/10: Diversity metrics - FAILED")
    
    # Test 9: Performance monitoring
    if test_performance_monitoring():
        tests_passed += 1
        logger.info("✓ Test 9/10: Performance monitoring - PASSED")
    else:
        logger.error("✗ Test 9/10: Performance monitoring - FAILED")
    
    # Test 10: Integration with existing system
    if test_integration_with_existing_system():
        tests_passed += 1
        logger.info("✓ Test 10/10: Integration with existing system - PASSED")
    else:
        logger.error("✗ Test 10/10: Integration with existing system - FAILED")
    
    # Test 11: Error handling (bonus test)
    if test_error_handling():
        logger.info("✓ Bonus Test: Error handling - PASSED")
    else:
        logger.error("✗ Bonus Test: Error handling - FAILED")
    
    # Summary
    execution_time = time.time() - start_time
    logger.info("=" * 60)
    logger.info("Phase 6 Test Results Summary")
    logger.info("=" * 60)
    logger.info(f"Tests passed: {tests_passed}/{total_tests}")
    logger.info(f"Success rate: {(tests_passed/total_tests)*100:.1f}%")
    logger.info(f"Execution time: {execution_time:.2f} seconds")
    
    if tests_passed == total_tests:
        logger.info("🎉 All Phase 6 tests passed! Advanced features are ready for use.")
        return True
    else:
        logger.error(f"❌ {total_tests - tests_passed} tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 