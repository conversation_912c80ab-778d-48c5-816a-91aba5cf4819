#!/usr/bin/env python3
"""
Test script for Cross-Category Retrieval System

This script tests the intelligent category routing and cross-category retrieval
functionality, including category weighting, routing strategies, and integration
with the main query service.
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from app.services.category_router import route_query_to_categories, CategoryWeight, CategoryRoutingResult
        print("✅ Category router imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import category router: {e}")
        return False
    
    try:
        from app.services.cross_category_retriever import retrieve_cross_category, CrossCategoryResult
        print("✅ Cross-category retriever imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import cross-category retriever: {e}")
        return False
    
    try:
        from app.services.query_service import query_category
        print("✅ Query service imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import query service: {e}")
        return False
    
    try:
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config, CrossCategoryConfig
        print("✅ Enhanced retrieval config imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import enhanced retrieval config: {e}")
        return False
    
    return True

def test_category_router():
    """Test the category router functionality"""
    print("\n🔍 Testing category router...")
    
    try:
        from app.services.category_router import route_query_to_categories, category_router
        
        # Test queries for different categories
        test_queries = [
            ("What are the best practices for forest management?", "forestry"),
            ("How do I conduct research on biodiversity?", "research"),
            ("What is the procedure for tree planting?", "procedural"),
            ("Tell me about sustainable forestry in the Philippines", "comprehensive"),
            ("What are the latest developments in forest conservation?", "research")
        ]
        
        for query, expected_type in test_queries:
            print(f"\n📝 Testing query: '{query}'")
            
            result = route_query_to_categories(query)
            
            print(f"   Routing strategy: {result.routing_strategy}")
            print(f"   Cross-category threshold: {result.cross_category_threshold:.3f}")
            print(f"   Execution time: {result.execution_time:.3f}s")
            
            print("   Primary categories:")
            for weight in result.primary_categories:
                print(f"     - {weight.category}: {weight.weight:.3f} (confidence: {weight.confidence:.3f})")
                print(f"       Reasoning: {weight.reasoning}")
            
            print("   Secondary categories:")
            for weight in result.secondary_categories:
                print(f"     - {weight.category}: {weight.weight:.3f} (confidence: {weight.confidence:.3f})")
        
        # Test category statistics
        stats = category_router.get_category_statistics()
        print(f"\n📊 Category statistics: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Category router test failed: {e}")
        return False

def test_cross_category_retriever():
    """Test the cross-category retriever functionality"""
    print("\n🔍 Testing cross-category retriever...")
    
    try:
        from app.services.cross_category_retriever import retrieve_cross_category
        
        # Test queries
        test_queries = [
            "What are the best practices for sustainable forest management in the Philippines?",
            "How do I conduct research on biodiversity conservation?",
            "What procedures should I follow for tree planting and maintenance?"
        ]
        
        for query in test_queries:
            print(f"\n📝 Testing cross-category retrieval for: '{query}'")
            
            # Test with user context
            user_context = {
                'client_name': 'test_user',
                'session_id': 'test_session',
                'recent_categories': ['CANOPY'],
                'preferred_categories': ['CANOPY', 'RISE']
            }
            
            result = retrieve_cross_category(
                query=query,
                user_context=user_context,
                max_total_documents=20
            )
            
            print(f"   Total documents retrieved: {result.total_documents_retrieved}")
            print(f"   Execution time: {result.execution_time:.3f}s")
            print(f"   Cache hit rate: {result.cache_hit_rate:.3f}")
            print(f"   Search type: {result.metadata.get('search_type', 'unknown')}")
            
            print("   Documents per category:")
            for category, count in result.documents_per_category.items():
                print(f"     - {category}: {count} documents")
            
            print("   Category weights:")
            for weight in result.category_weights:
                print(f"     - {weight.category}: {weight.weight:.3f}")
            
            if result.documents:
                print(f"   Sample document sources:")
                for i, doc in enumerate(result.documents[:3]):
                    source = doc.metadata.get('source', 'Unknown')
                    category = doc.metadata.get('category', 'Unknown')
                    print(f"     {i+1}. {source} ({category})")
        
        return True
        
    except Exception as e:
        print(f"❌ Cross-category retriever test failed: {e}")
        return False

def test_query_service_integration():
    """Test integration with the main query service"""
    print("\n🔍 Testing query service integration...")
    
    try:
        from app.services.query_service import query_category
        
        # Test cross-category query
        test_query = "What are the best practices for forest management and research?"
        
        print(f"📝 Testing cross-category query: '{test_query}'")
        
        # Test with cross-category enabled
        result = query_category(
            category="CANOPY",  # This will be used as a fallback/preference
            question=test_query,
            anti_hallucination_mode="balanced",
            use_cross_category=True
        )
        
        print(f"   Answer length: {len(result.get('answer', ''))} characters")
        print(f"   Processing time: {result.get('processing_time', 0):.3f}s")
        print(f"   Category: {result.get('category', 'unknown')}")
        print(f"   Sources found: {len(result.get('sources', []))}")
        
        # Test without cross-category (regular single-category query)
        print(f"\n📝 Testing single-category query: '{test_query}'")
        
        result_single = query_category(
            category="CANOPY",
            question=test_query,
            anti_hallucination_mode="balanced",
            use_cross_category=False
        )
        
        print(f"   Answer length: {len(result_single.get('answer', ''))} characters")
        print(f"   Processing time: {result_single.get('processing_time', 0):.3f}s")
        print(f"   Category: {result_single.get('category', 'unknown')}")
        print(f"   Sources found: {len(result_single.get('sources', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Query service integration test failed: {e}")
        return False

def test_configuration():
    """Test the cross-category configuration"""
    print("\n🔍 Testing configuration...")
    
    try:
        from config.enhanced_retrieval_config import get_enhanced_retrieval_config
        
        config = get_enhanced_retrieval_config()
        
        print("✅ Configuration loaded successfully")
        print(f"   Cross-category enabled: {config.cross_category.enable_intelligent_routing}")
        print(f"   Max total documents: {config.cross_category.max_total_documents}")
        print(f"   Cache TTL: {config.cross_category.cache_ttl} seconds")
        print(f"   Parallel workers: {config.cross_category.max_parallel_workers}")
        
        print("   Routing strategies:")
        for strategy, details in config.cross_category.routing_strategies.items():
            print(f"     - {strategy}: {details['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_performance_monitoring():
    """Test performance monitoring integration"""
    print("\n🔍 Testing performance monitoring...")
    
    try:
        from app.utils.rag_performance import get_rag_monitor
        
        monitor = get_rag_monitor()
        
        # Simulate some metrics
        monitor.record_query_metric('CANOPY', 1.5, 15)
        monitor.record_cache_hit('cross_category')
        monitor.record_cache_miss('cross_category')
        
        print("✅ Performance monitoring integration working")
        print(f"   Total metrics recorded: {len(monitor.metrics)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Cross-Category Retrieval System Tests")
    print("=" * 60)
    
    test_results = []
    
    # Run tests
    tests = [
        ("Imports", test_imports),
        ("Category Router", test_category_router),
        ("Cross-Category Retriever", test_cross_category_retriever),
        ("Query Service Integration", test_query_service_integration),
        ("Configuration", test_configuration),
        ("Performance Monitoring", test_performance_monitoring)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            test_results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cross-category retrieval system is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 